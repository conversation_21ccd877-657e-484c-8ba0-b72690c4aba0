{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}Bulk Upload AWS Accounts{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .upload-container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .upload-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }
    
    .upload-header h1 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .upload-header p {
        color: #666;
        font-size: 14px;
    }
    
    .upload-form {
        margin-bottom: 30px;
    }
    
    .file-input-container {
        position: relative;
        display: inline-block;
        width: 100%;
        margin-bottom: 20px;
    }
    
    .file-input {
        width: 100%;
        padding: 12px;
        border: 2px dashed #ddd;
        border-radius: 8px;
        background: #f9f9f9;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .file-input:hover {
        border-color: #007cba;
        background: #f0f8ff;
    }
    
    .upload-btn {
        background: #007cba;
        color: white;
        padding: 12px 30px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        transition: background 0.3s ease;
    }
    
    .upload-btn:hover {
        background: #005a87;
    }
    
    .upload-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
    }
    
    .csv-format {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .csv-format h3 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 18px;
    }
    
    .csv-example {
        background: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        overflow-x: auto;
        margin-bottom: 15px;
    }
    
    .csv-example .header {
        font-weight: bold;
        color: #007cba;
    }
    
    .requirements {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .requirements h4 {
        color: #856404;
        margin-bottom: 10px;
    }
    
    .requirements ul {
        margin: 0;
        padding-left: 20px;
    }
    
    .requirements li {
        color: #856404;
        margin-bottom: 5px;
    }
    
    .download-template {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .download-btn {
        background: #28a745;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 5px;
        display: inline-block;
        transition: background 0.3s ease;
    }
    
    .download-btn:hover {
        background: #218838;
        color: white;
        text-decoration: none;
    }
    
    .back-link {
        margin-top: 20px;
        text-align: center;
    }
    
    .back-link a {
        color: #007cba;
        text-decoration: none;
    }
    
    .back-link a:hover {
        text-decoration: underline;
    }
</style>
{% endblock %}

{% block content %}
<div class="upload-container">
    <div class="upload-header">
        <h1>
            <i class="fas fa-cloud-upload-alt"></i>
            Bulk Upload AWS Accounts
        </h1>
        <p>Upload multiple AWS accounts from a CSV file</p>
    </div>

    <!-- CSV Format Information -->
    <div class="csv-format">
        <h3><i class="fas fa-file-csv"></i> CSV Format Requirements</h3>
        
        <div class="csv-example">
            <div class="header">account_id,account_name,region,bu</div>
            <div>************,capital-stage,us-east-1,HQ CTO Vernova</div>
            <div>************,production-env,us-west-2,Gas Power</div>
            <div>************,development-env,eu-west-1,Renewable Energy</div>
        </div>
        
        <div class="requirements">
            <h4><i class="fas fa-exclamation-triangle"></i> Requirements:</h4>
            <ul>
                <li><strong>account_id</strong>: Must be exactly 12 digits (required)</li>
                <li><strong>account_name</strong>: Descriptive name for the account (required)</li>
                <li><strong>region</strong>: AWS region (e.g., us-east-1, us-west-2) (required)</li>
                <li><strong>bu</strong>: Business Unit (e.g., "HQ CTO Vernova", "Gas Power") (required)</li>
                <li>CSV file must include header row with exact column names</li>
                <li>All fields are required for each row</li>
                <li>Duplicate account IDs will update existing records</li>
            </ul>
        </div>
        
        <div class="download-template">
            <a href="data:text/csv;charset=utf-8,account_id%2Caccount_name%2Cregion%2Cbu%0A************%2Ccapital-stage%2Cus-east-1%2CHQ%20CTO%20Vernova%0A************%2Cproduction-env%2Cus-west-2%2CGas%20Power%0A************%2Cdevelopment-env%2Ceu-west-1%2CRenewable%20Energy" 
               download="aws_accounts_template.csv" 
               class="download-btn">
                <i class="fas fa-download"></i> Download Template CSV
            </a>
        </div>
    </div>

    <!-- Upload Form -->
    <form method="post" enctype="multipart/form-data" class="upload-form" id="uploadForm">
        {% csrf_token %}
        
        <div class="file-input-container">
            <input type="file" 
                   name="csv_file" 
                   accept=".csv" 
                   required 
                   class="file-input"
                   id="csvFile"
                   onchange="updateFileName()">
            <label for="csvFile" style="display: block; text-align: center; padding: 20px; cursor: pointer;">
                <i class="fas fa-cloud-upload-alt" style="font-size: 24px; color: #007cba; margin-bottom: 10px;"></i><br>
                <span id="fileLabel">Click to select CSV file or drag and drop</span>
            </label>
        </div>
        
        <div style="text-align: center;">
            <button type="submit" class="upload-btn" id="uploadBtn" disabled>
                <i class="fas fa-upload"></i> Upload AWS Accounts
            </button>
        </div>
    </form>

    <!-- Back Link -->
    <div class="back-link">
        <a href="{% url 'admin:inventory_awsaccount_changelist' %}">
            <i class="fas fa-arrow-left"></i> Back to AWS Accounts
        </a>
    </div>
</div>

<script>
function updateFileName() {
    const fileInput = document.getElementById('csvFile');
    const fileLabel = document.getElementById('fileLabel');
    const uploadBtn = document.getElementById('uploadBtn');
    
    if (fileInput.files.length > 0) {
        const fileName = fileInput.files[0].name;
        fileLabel.innerHTML = `<i class="fas fa-file-csv"></i> ${fileName}`;
        uploadBtn.disabled = false;
    } else {
        fileLabel.innerHTML = 'Click to select CSV file or drag and drop';
        uploadBtn.disabled = true;
    }
}

// Drag and drop functionality
const fileInput = document.getElementById('csvFile');
const container = document.querySelector('.file-input-container');

container.addEventListener('dragover', function(e) {
    e.preventDefault();
    container.style.borderColor = '#007cba';
    container.style.background = '#f0f8ff';
});

container.addEventListener('dragleave', function(e) {
    e.preventDefault();
    container.style.borderColor = '#ddd';
    container.style.background = '#f9f9f9';
});

container.addEventListener('drop', function(e) {
    e.preventDefault();
    container.style.borderColor = '#ddd';
    container.style.background = '#f9f9f9';
    
    const files = e.dataTransfer.files;
    if (files.length > 0 && files[0].name.endsWith('.csv')) {
        fileInput.files = files;
        updateFileName();
    }
});

// Form submission handling
document.getElementById('uploadForm').addEventListener('submit', function() {
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
});
</script>
{% endblock %}
