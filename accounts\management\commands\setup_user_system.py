#!/usr/bin/env python3
"""
Django management command to set up the user management system
Creates default roles and admin user
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from accounts.models import UserRole

User = get_user_model()


class Command(BaseCommand):
    help = 'Set up user management system with default roles and admin user'

    def add_arguments(self, parser):
        parser.add_argument(
            '--admin-username',
            type=str,
            default='admin',
            help='Admin username (default: admin)'
        )
        parser.add_argument(
            '--admin-email',
            type=str,
            default='<EMAIL>',
            help='Admin email (default: <EMAIL>)'
        )
        parser.add_argument(
            '--admin-password',
            type=str,
            default='admin123',
            help='Admin password (default: admin123)'
        )
        parser.add_argument(
            '--skip-admin',
            action='store_true',
            help='Skip admin user creation'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up user management system...'))
        
        # Create default roles
        self.create_default_roles()
        
        # Create admin user if requested
        if not options['skip_admin']:
            self.create_admin_user(
                options['admin_username'],
                options['admin_email'],
                options['admin_password']
            )
        
        self.stdout.write(self.style.SUCCESS('✅ User management system setup completed!'))
        self.print_summary()

    def create_default_roles(self):
        """Create default user roles"""
        self.stdout.write('\n📋 Creating default roles...')
        
        roles_data = [
            {
                'name': 'admin',
                'display_name': 'Admin',
                'description': 'Full access to all features including user management, system configuration, and all operations.'
            },
            {
                'name': 'automation_user',
                'display_name': 'Automation User',
                'description': 'Read access to all inventory and reports, plus execute access for deployment tasks and automation workflows.'
            },
            {
                'name': 'reader',
                'display_name': 'Reader',
                'description': 'Read-only access to inventory, automation reports, and decommission reports.'
            }
        ]
        
        created_count = 0
        for role_data in roles_data:
            role, created = UserRole.objects.get_or_create(
                name=role_data['name'],
                defaults=role_data
            )
            
            if created:
                self.stdout.write(f"  ✅ Created role: {role.display_name}")
                created_count += 1
            else:
                self.stdout.write(f"  ⚠️  Role already exists: {role.display_name}")
        
        self.stdout.write(f"\n📊 Roles summary: {created_count} created, {len(roles_data) - created_count} already existed")

    def create_admin_user(self, username, email, password):
        """Create admin user"""
        self.stdout.write('\n👤 Creating admin user...')
        
        try:
            # Check if admin user already exists
            if User.objects.filter(username=username).exists():
                self.stdout.write(f"  ⚠️  User '{username}' already exists")
                return
            
            # Get admin role
            admin_role = UserRole.objects.get(name='admin')
            
            # Create admin user
            admin_user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name='System',
                last_name='Administrator',
                is_staff=True,
                is_superuser=True,
                is_active=True,
                is_approved=True,
                user_role=admin_role,
                approved_at=timezone.now()
            )
            
            self.stdout.write(f"  ✅ Created admin user: {username}")
            self.stdout.write(f"     Email: {email}")
            self.stdout.write(f"     Password: {password}")
            self.stdout.write(f"     Role: {admin_role.display_name}")
            
        except UserRole.DoesNotExist:
            self.stdout.write(self.style.ERROR("  ❌ Admin role not found. Please run role creation first."))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"  ❌ Error creating admin user: {str(e)}"))

    def print_summary(self):
        """Print setup summary"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS(' USER MANAGEMENT SYSTEM SETUP SUMMARY'))
        self.stdout.write('='*60)
        
        # Roles summary
        roles = UserRole.objects.all()
        self.stdout.write(f"\n📋 Roles ({roles.count()}):")
        for role in roles:
            self.stdout.write(f"   • {role.display_name} ({role.name})")
            self.stdout.write(f"     {role.description}")
        
        # Users summary
        users = User.objects.all()
        self.stdout.write(f"\n👥 Users ({users.count()}):")
        for user in users:
            status_indicators = []
            if user.is_superuser:
                status_indicators.append("Superuser")
            if user.is_staff:
                status_indicators.append("Staff")
            if user.is_approved:
                status_indicators.append("Approved")
            if not user.is_active:
                status_indicators.append("Inactive")
            
            status_str = f" [{', '.join(status_indicators)}]" if status_indicators else ""
            role_str = f" - {user.get_role_display()}" if user.user_role else ""
            
            self.stdout.write(f"   • {user.username} ({user.email}){role_str}{status_str}")
        
        # Next steps
        self.stdout.write(f"\n🚀 Next Steps:")
        self.stdout.write(f"   1. Run migrations: python manage.py migrate")
        self.stdout.write(f"   2. Start server: python manage.py runserver")
        self.stdout.write(f"   3. Access login page: http://127.0.0.1:8000/accounts/login/")
        self.stdout.write(f"   4. Login with admin credentials")
        self.stdout.write(f"   5. Create additional users via admin interface or registration")
        
        # Security notes
        self.stdout.write(f"\n🔒 Security Notes:")
        self.stdout.write(f"   • Change default admin password after first login")
        self.stdout.write(f"   • Review user roles and permissions")
        self.stdout.write(f"   • Configure SAML SSO for production use")
        self.stdout.write(f"   • Enable HTTPS in production")
        
        self.stdout.write('\n' + '='*60)


def create_sample_users():
    """Create sample users for testing"""
    sample_users = [
        {
            'username': 'john.doe',
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Doe',
            'role': 'automation_user',
            'department': 'IT Operations',
            'business_unit': 'Gas Power'
        },
        {
            'username': 'jane.smith',
            'email': '<EMAIL>',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'role': 'reader',
            'department': 'Finance',
            'business_unit': 'HQ CTO Vernova'
        },
        {
            'username': 'mike.wilson',
            'email': '<EMAIL>',
            'first_name': 'Mike',
            'last_name': 'Wilson',
            'role': 'automation_user',
            'department': 'DevOps',
            'business_unit': 'Renewable Energy'
        }
    ]
    
    created_count = 0
    for user_data in sample_users:
        role_name = user_data.pop('role')
        
        if not User.objects.filter(username=user_data['username']).exists():
            try:
                role = UserRole.objects.get(name=role_name)
                user = User.objects.create_user(
                    password='password123',  # Default password
                    user_role=role,
                    is_approved=True,
                    approved_at=timezone.now(),
                    **user_data
                )
                created_count += 1
                print(f"Created sample user: {user.username}")
            except UserRole.DoesNotExist:
                print(f"Role {role_name} not found for user {user_data['username']}")
    
    return created_count
