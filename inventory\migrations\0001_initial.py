# Generated by Django 4.2.7 on 2025-06-11 19:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AWSAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_id', models.CharField(max_length=12, unique=True)),
                ('account_name', models.CharField(blank=True, max_length=255)),
                ('region', models.CharField(max_length=50)),
                ('business_unit', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['business_unit', 'account_name'],
            },
        ),
        migrations.CreateModel(
            name='EC2Instance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('instance_id', models.CharField(db_index=True, max_length=20)),
                ('name', models.CharField(blank=True, db_index=True, max_length=255)),
                ('state', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('shutting-down', 'Shutting Down'), ('terminated', 'Terminated'), ('stopping', 'Stopping'), ('stopped', 'Stopped')], db_index=True, max_length=20)),
                ('instance_type', models.CharField(db_index=True, max_length=50)),
                ('vcpu_count', models.IntegerField(blank=True, null=True)),
                ('memory_gb', models.FloatField(blank=True, null=True)),
                ('private_ip_address', models.GenericIPAddressField(blank=True, db_index=True, null=True)),
                ('private_dns_name', models.CharField(blank=True, max_length=255)),
                ('subnet_id', models.CharField(blank=True, max_length=50)),
                ('subnet_name', models.CharField(blank=True, max_length=255)),
                ('security_groups', models.TextField(blank=True)),
                ('instance_profile', models.CharField(blank=True, max_length=255)),
                ('root_volume_id', models.CharField(blank=True, max_length=50)),
                ('root_volume_size_gb', models.IntegerField(blank=True, null=True)),
                ('data_disk_count', models.IntegerField(default=0)),
                ('os_information', models.CharField(blank=True, max_length=255)),
                ('ami_id', models.CharField(blank=True, max_length=50)),
                ('platform_type', models.CharField(blank=True, max_length=50)),
                ('env_tag', models.CharField(blank=True, db_index=True, max_length=100)),
                ('uai_tag', models.CharField(blank=True, db_index=True, max_length=100)),
                ('patch_tag', models.CharField(blank=True, max_length=100)),
                ('app_env_cfg_id', models.CharField(blank=True, max_length=100)),
                ('maintenance_schedule', models.CharField(blank=True, max_length=100)),
                ('schedule_tag', models.CharField(blank=True, max_length=100)),
                ('backup_tag', models.CharField(blank=True, max_length=100)),
                ('cto_cloud_ops_managed', models.CharField(blank=True, db_index=True, max_length=100)),
                ('patch_provider', models.CharField(blank=True, max_length=100)),
                ('patch_group', models.CharField(blank=True, max_length=100)),
                ('patch_exempt', models.CharField(blank=True, max_length=100)),
                ('all_tags_raw', models.TextField(blank=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-last_updated'],
            },
        ),
        migrations.CreateModel(
            name='EKSCluster',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cluster_name', models.CharField(db_index=True, max_length=255)),
                ('arn', models.CharField(max_length=500, unique=True)),
                ('kubernetes_version', models.CharField(blank=True, max_length=20)),
                ('platform_version', models.CharField(blank=True, max_length=50)),
                ('status', models.CharField(choices=[('CREATING', 'Creating'), ('ACTIVE', 'Active'), ('DELETING', 'Deleting'), ('FAILED', 'Failed'), ('UPDATING', 'Updating')], db_index=True, max_length=20)),
                ('endpoint', models.URLField(blank=True)),
                ('cluster_type', models.CharField(choices=[('EC2', 'EC2'), ('Fargate', 'Fargate'), ('Hybrid', 'Hybrid (EC2 + Fargate)')], default='EC2', max_length=20)),
                ('vpc_id', models.CharField(blank=True, max_length=50)),
                ('subnet_ids', models.TextField(blank=True)),
                ('security_group_ids', models.TextField(blank=True)),
                ('endpoint_public_access', models.BooleanField(default=True)),
                ('endpoint_private_access', models.BooleanField(default=False)),
                ('public_access_cidrs', models.TextField(blank=True)),
                ('service_ipv4_cidr', models.CharField(blank=True, max_length=50)),
                ('service_ipv6_cidr', models.CharField(blank=True, max_length=50)),
                ('ip_family', models.CharField(blank=True, max_length=10)),
                ('logging_enabled', models.BooleanField(default=False)),
                ('logging_types', models.TextField(blank=True)),
                ('encryption_enabled', models.BooleanField(default=False)),
                ('encryption_key_arn', models.CharField(blank=True, max_length=500)),
                ('role_arn', models.CharField(blank=True, max_length=500)),
                ('identity_oidc_issuer', models.URLField(blank=True)),
                ('addons', models.TextField(blank=True)),
                ('fargate_profile_names', models.TextField(blank=True)),
                ('node_groups_count', models.IntegerField(default=0)),
                ('fargate_profiles_count', models.IntegerField(default=0)),
                ('all_tags_raw', models.TextField(blank=True)),
                ('created_at_aws', models.DateTimeField(blank=True, null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-last_updated'],
            },
        ),
        migrations.CreateModel(
            name='EKSClusterTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_index=True, max_length=255)),
                ('value', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['key', 'value'],
            },
        ),
        migrations.CreateModel(
            name='SSMStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ping_status', models.CharField(blank=True, choices=[('Online', 'Online'), ('Connection Lost', 'Connection Lost'), ('Inactive', 'Inactive'), ('Unknown', 'Unknown')], db_index=True, max_length=20)),
                ('agent_version', models.CharField(blank=True, max_length=50)),
                ('is_latest_version', models.BooleanField(blank=True, null=True)),
                ('last_ping_datetime', models.DateTimeField(blank=True, null=True)),
                ('platform_name', models.CharField(blank=True, max_length=100)),
                ('platform_type', models.CharField(blank=True, max_length=50)),
                ('ssm_configured', models.BooleanField(default=False)),
                ('last_checked', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('instance', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='ssm_status', to='inventory.ec2instance')),
            ],
            options={
                'ordering': ['-last_checked'],
            },
        ),
        migrations.CreateModel(
            name='InventoryRefreshLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='running', max_length=20)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('instances_processed', models.IntegerField(default=0)),
                ('errors_count', models.IntegerField(default=0)),
                ('error_details', models.TextField(blank=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refresh_logs', to='inventory.awsaccount')),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='InstanceTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_index=True, max_length=255)),
                ('value', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['key', 'value'],
                'indexes': [models.Index(fields=['key'], name='inventory_i_key_1cbd62_idx'), models.Index(fields=['key', 'value'], name='inventory_i_key_2cf2cd_idx')],
            },
        ),
        migrations.CreateModel(
            name='EKSNodeGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('node_group_name', models.CharField(db_index=True, max_length=255)),
                ('arn', models.CharField(max_length=500, unique=True)),
                ('status', models.CharField(choices=[('CREATING', 'Creating'), ('ACTIVE', 'Active'), ('UPDATING', 'Updating'), ('DELETING', 'Deleting'), ('CREATE_FAILED', 'Create Failed'), ('DELETE_FAILED', 'Delete Failed'), ('DEGRADED', 'Degraded')], db_index=True, max_length=20)),
                ('capacity_type', models.CharField(choices=[('ON_DEMAND', 'On Demand'), ('SPOT', 'Spot')], default='ON_DEMAND', max_length=20)),
                ('instance_types', models.TextField(blank=True)),
                ('ami_type', models.CharField(blank=True, choices=[('AL2_x86_64', 'Amazon Linux 2 (x86_64)'), ('AL2_x86_64_GPU', 'Amazon Linux 2 GPU (x86_64)'), ('AL2_ARM_64', 'Amazon Linux 2 (ARM64)'), ('CUSTOM', 'Custom'), ('BOTTLEROCKET_ARM_64', 'Bottlerocket (ARM64)'), ('BOTTLEROCKET_x86_64', 'Bottlerocket (x86_64)')], max_length=50)),
                ('node_role_arn', models.CharField(blank=True, max_length=500)),
                ('desired_size', models.IntegerField(default=0)),
                ('min_size', models.IntegerField(default=0)),
                ('max_size', models.IntegerField(default=0)),
                ('subnet_ids', models.TextField(blank=True)),
                ('remote_access_enabled', models.BooleanField(default=False)),
                ('remote_access_source_sg', models.TextField(blank=True)),
                ('launch_template_id', models.CharField(blank=True, max_length=50)),
                ('launch_template_version', models.CharField(blank=True, max_length=20)),
                ('disk_size', models.IntegerField(blank=True, null=True)),
                ('all_tags_raw', models.TextField(blank=True)),
                ('created_at_aws', models.DateTimeField(blank=True, null=True)),
                ('modified_at_aws', models.DateTimeField(blank=True, null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('cluster', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='node_groups', to='inventory.ekscluster')),
                ('tags', models.ManyToManyField(blank=True, related_name='node_groups', to='inventory.eksclustertag')),
            ],
            options={
                'ordering': ['-last_updated'],
            },
        ),
        migrations.CreateModel(
            name='EKSFargateProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('profile_name', models.CharField(db_index=True, max_length=255)),
                ('arn', models.CharField(max_length=500, unique=True)),
                ('status', models.CharField(choices=[('CREATING', 'Creating'), ('ACTIVE', 'Active'), ('DELETING', 'Deleting'), ('CREATE_FAILED', 'Create Failed'), ('DELETE_FAILED', 'Delete Failed')], db_index=True, max_length=20)),
                ('pod_execution_role_arn', models.CharField(blank=True, max_length=500)),
                ('subnet_ids', models.TextField(blank=True)),
                ('selectors', models.TextField(blank=True)),
                ('all_tags_raw', models.TextField(blank=True)),
                ('created_at_aws', models.DateTimeField(blank=True, null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('cluster', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fargate_profiles', to='inventory.ekscluster')),
                ('tags', models.ManyToManyField(blank=True, related_name='fargate_profiles', to='inventory.eksclustertag')),
            ],
            options={
                'ordering': ['-last_updated'],
            },
        ),
        migrations.AddIndex(
            model_name='eksclustertag',
            index=models.Index(fields=['key'], name='inventory_e_key_64ac85_idx'),
        ),
        migrations.AddIndex(
            model_name='eksclustertag',
            index=models.Index(fields=['key', 'value'], name='inventory_e_key_24363f_idx'),
        ),
        migrations.AddField(
            model_name='ekscluster',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='eks_clusters', to='inventory.awsaccount'),
        ),
        migrations.AddField(
            model_name='ekscluster',
            name='tags',
            field=models.ManyToManyField(blank=True, related_name='clusters', to='inventory.eksclustertag'),
        ),
        migrations.AddField(
            model_name='ec2instance',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ec2_instances', to='inventory.awsaccount'),
        ),
        migrations.AddField(
            model_name='ec2instance',
            name='tags',
            field=models.ManyToManyField(blank=True, related_name='instances', to='inventory.instancetag'),
        ),
        migrations.AddIndex(
            model_name='ssmstatus',
            index=models.Index(fields=['ping_status'], name='inventory_s_ping_st_b99f72_idx'),
        ),
        migrations.AddIndex(
            model_name='ssmstatus',
            index=models.Index(fields=['is_latest_version'], name='inventory_s_is_late_8386a0_idx'),
        ),
        migrations.AddIndex(
            model_name='eksnodegroup',
            index=models.Index(fields=['cluster', 'node_group_name'], name='inventory_e_cluster_2a1828_idx'),
        ),
        migrations.AddIndex(
            model_name='eksnodegroup',
            index=models.Index(fields=['status', 'capacity_type'], name='inventory_e_status_492a1f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='eksnodegroup',
            unique_together={('cluster', 'node_group_name')},
        ),
        migrations.AlterUniqueTogether(
            name='eksfargateprofile',
            unique_together={('cluster', 'profile_name')},
        ),
        migrations.AddIndex(
            model_name='ekscluster',
            index=models.Index(fields=['account', 'cluster_name'], name='inventory_e_account_be7fa1_idx'),
        ),
        migrations.AddIndex(
            model_name='ekscluster',
            index=models.Index(fields=['status', 'cluster_type'], name='inventory_e_status_850fe0_idx'),
        ),
        migrations.AddIndex(
            model_name='ekscluster',
            index=models.Index(fields=['kubernetes_version'], name='inventory_e_kuberne_38ec9b_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='ekscluster',
            unique_together={('account', 'cluster_name')},
        ),
        migrations.AddIndex(
            model_name='ec2instance',
            index=models.Index(fields=['account', 'instance_id'], name='inventory_e_account_349302_idx'),
        ),
        migrations.AddIndex(
            model_name='ec2instance',
            index=models.Index(fields=['state', 'instance_type'], name='inventory_e_state_9df6a6_idx'),
        ),
        migrations.AddIndex(
            model_name='ec2instance',
            index=models.Index(fields=['env_tag', 'uai_tag'], name='inventory_e_env_tag_7d4224_idx'),
        ),
        migrations.AddIndex(
            model_name='ec2instance',
            index=models.Index(fields=['cto_cloud_ops_managed'], name='inventory_e_cto_clo_ba8a45_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='ec2instance',
            unique_together={('account', 'instance_id')},
        ),
    ]
