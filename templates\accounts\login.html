<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Cloud Operations Central</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-weight: 300;
            font-size: 1.8rem;
        }
        
        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-floating input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .form-floating input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            margin: 1rem 0;
        }
        
        .remember-me input {
            margin-right: 0.5rem;
        }
        
        .register-link {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .register-link a:hover {
            text-decoration: underline;
        }
        
        .alert {
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        
        .role-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.85rem;
        }
        
        .role-info h6 {
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .role-info ul {
            margin: 0;
            padding-left: 1.2rem;
        }
        
        .role-info li {
            margin-bottom: 0.2rem;
            color: #6c757d;
        }
        
        .saml-login {
            margin-top: 1rem;
            text-align: center;
        }
        
        .btn-saml {
            background: #0078d4;
            border: none;
            border-radius: 10px;
            color: white;
            padding: 0.75rem;
            width: 100%;
            margin-top: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-saml:hover {
            background: #106ebe;
            transform: translateY(-1px);
        }
        
        .divider {
            text-align: center;
            margin: 1rem 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        
        .divider span {
            background: white;
            padding: 0 1rem;
            color: #6c757d;
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="fas fa-cloud fa-2x mb-2"></i>
            <h2>Cloud Operations Central</h2>
            <p>Secure Access Portal</p>
        </div>
        
        <div class="login-body">
            <!-- Display messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <!-- Login Form -->
            <form method="post">
                {% csrf_token %}
                
                <div class="form-floating">
                    {{ form.username }}
                    <label for="{{ form.username.id_for_label }}">
                        <i class="fas fa-user me-2"></i>Username or Email
                    </label>
                    {% if form.username.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.username.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-floating">
                    {{ form.password }}
                    <label for="{{ form.password.id_for_label }}">
                        <i class="fas fa-lock me-2"></i>Password
                    </label>
                    {% if form.password.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="remember-me">
                    {{ form.remember_me }}
                    <label for="{{ form.remember_me.id_for_label }}" class="form-check-label">
                        Remember me for 2 weeks
                    </label>
                </div>
                
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                
                <button type="submit" class="btn btn-primary btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                </button>
            </form>
            
            <!-- Future SAML SSO Section -->
            <div class="divider">
                <span>or</span>
            </div>
            
            <div class="saml-login">
                <button type="button" class="btn btn-saml" onclick="showSAMLInfo()">
                    <i class="fab fa-microsoft me-2"></i>Sign in with SSO
                    <small class="d-block">Coming Soon</small>
                </button>
            </div>
            
            <!-- Register Link -->
            <div class="register-link">
                <p class="mb-0">
                    Don't have an account?
                    <a href="{% url 'accounts:register' %}">Request Access</a>
                </p>
            </div>
            
            <!-- Role Information -->
            <div class="role-info">
                <h6><i class="fas fa-info-circle me-1"></i>Access Levels</h6>
                <ul class="small">
                    <li><strong>Reader:</strong> View inventory and reports</li>
                    <li><strong>Automation User:</strong> Execute deployments</li>
                    <li><strong>Admin:</strong> Full system access</li>
                </ul>
                <p class="small text-muted mb-0 mt-2">
                    <i class="fas fa-shield-alt me-1"></i>
                    All accounts require administrator approval
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showSAMLInfo() {
            alert('SAML SSO integration is coming soon! This will allow you to sign in with your corporate credentials.');
        }
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('{{ form.username.id_for_label }}');
            if (usernameField) {
                usernameField.focus();
            }
        });
    </script>
</body>
</html>
