#!/usr/bin/env python
"""
Test script to verify the new dashboard layout with AWS and Azure sections
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from django.test import Client
from inventory.models import AWSAccount, EC2Instance
import json

def print_header(title):
    print("\n" + "="*70)
    print(f" {title}")
    print("="*70)

def print_section(title):
    print(f"\n--- {title} ---")

def test_dashboard_layout():
    print_header("TESTING NEW DASHBOARD LAYOUT")
    
    client = Client()
    
    print_section("Dashboard Page Load Test")
    
    response = client.get('/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check for AWS section
        has_aws_section = 'AWS' in content and 'fab fa-aws' in content
        has_aws_accounts_table = 'aws-accounts-table' in content
        has_aws_ec2_table = 'aws-ec2-table' in content
        has_aws_eks_table = 'aws-eks-table' in content
        
        # Check for Azure section
        has_azure_section = 'Azure' in content and 'fab fa-microsoft' in content
        has_azure_subscriptions_table = 'azure-subscriptions-table' in content
        has_azure_vm_table = 'azure-vm-table' in content
        has_azure_aks_table = 'azure-aks-table' in content
        
        # Check for removed elements
        no_refresh_button = 'Refresh All' not in content
        no_recent_instances = 'Recent EC2 Instances' not in content
        no_charts = 'stateChart' not in content and 'ssmChart' not in content
        
        print("✅ Dashboard page loads successfully")
        print(f"\n🔍 AWS Section:")
        print(f"   • AWS Header: {'✅' if has_aws_section else '❌'}")
        print(f"   • Accounts Table: {'✅' if has_aws_accounts_table else '❌'}")
        print(f"   • EC2 Table: {'✅' if has_aws_ec2_table else '❌'}")
        print(f"   • EKS Table: {'✅' if has_aws_eks_table else '❌'}")
        
        print(f"\n🔍 Azure Section:")
        print(f"   • Azure Header: {'✅' if has_azure_section else '❌'}")
        print(f"   • Subscriptions Table: {'✅' if has_azure_subscriptions_table else '❌'}")
        print(f"   • VM Table: {'✅' if has_azure_vm_table else '❌'}")
        print(f"   • AKS Table: {'✅' if has_azure_aks_table else '❌'}")
        
        print(f"\n🗑️  Removed Elements:")
        print(f"   • No Refresh Button: {'✅' if no_refresh_button else '❌'}")
        print(f"   • No Recent Instances: {'✅' if no_recent_instances else '❌'}")
        print(f"   • No Charts: {'✅' if no_charts else '❌'}")
        
        return all([
            has_aws_section, has_aws_accounts_table, has_aws_ec2_table,
            has_azure_section, has_azure_subscriptions_table, has_azure_vm_table,
            no_refresh_button, no_recent_instances
        ])
    else:
        print(f"❌ Dashboard failed to load: {response.status_code}")
        return False

def test_dashboard_data_structure():
    print_header("TESTING DASHBOARD DATA STRUCTURE")
    
    client = Client()
    
    print_section("AWS Data Analysis")
    
    # Get accounts data
    response = client.get('/api/accounts/?page_size=1000')
    if response.status_code == 200:
        accounts_data = response.json()['results']
        
        # Group by BU
        bu_accounts = {}
        for account in accounts_data:
            bu = account['business_unit']
            if bu not in bu_accounts:
                bu_accounts[bu] = []
            bu_accounts[bu].append(account)
        
        print("📊 AWS Accounts by Business Unit:")
        for bu, accounts in bu_accounts.items():
            print(f"   • {bu}: {len(accounts)} accounts")
        print(f"   • Total: {len(accounts_data)} accounts")
    
    # Get EC2 instances data
    response = client.get('/api/ec2-instances/?page_size=1000')
    if response.status_code == 200:
        instances_data = response.json()['results']
        
        # Group by BU and calculate stats
        bu_stats = {}
        for instance in instances_data:
            bu = instance['business_unit'] or 'Unknown'
            if bu not in bu_stats:
                bu_stats[bu] = {'total': 0, 'running': 0, 'ssm_online': 0}
            
            bu_stats[bu]['total'] += 1
            if instance['state'] == 'running':
                bu_stats[bu]['running'] += 1
            if instance['ssm_ping_status'] == 'Online':
                bu_stats[bu]['ssm_online'] += 1
        
        print("\n🖥️  AWS EC2 Instances by Business Unit:")
        for bu, stats in bu_stats.items():
            print(f"   • {bu}: {stats['total']} total, {stats['running']} running, {stats['ssm_online']} SSM online")
        
        total_instances = len(instances_data)
        total_running = sum(1 for i in instances_data if i['state'] == 'running')
        total_ssm = sum(1 for i in instances_data if i['ssm_ping_status'] == 'Online')
        print(f"   • Total: {total_instances} instances, {total_running} running, {total_ssm} SSM online")

def test_dashboard_javascript():
    print_header("TESTING DASHBOARD JAVASCRIPT FUNCTIONS")
    
    client = Client()
    
    response = client.get('/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check for new JavaScript functions
        has_load_aws_data = 'loadAWSData' in content
        has_update_accounts_table = 'updateAWSAccountsTable' in content
        has_update_ec2_table = 'updateAWSEC2Table' in content
        has_load_dashboard_data = 'loadDashboardData' in content
        
        # Check for removed functions
        no_chart_functions = 'updateCharts' not in content
        no_recent_instances_function = 'loadRecentInstances' not in content
        
        print("📜 JavaScript Functions:")
        print(f"   • loadAWSData: {'✅' if has_load_aws_data else '❌'}")
        print(f"   • updateAWSAccountsTable: {'✅' if has_update_accounts_table else '❌'}")
        print(f"   • updateAWSEC2Table: {'✅' if has_update_ec2_table else '❌'}")
        print(f"   • loadDashboardData: {'✅' if has_load_dashboard_data else '❌'}")
        
        print(f"\n🗑️  Removed Functions:")
        print(f"   • No updateCharts: {'✅' if no_chart_functions else '❌'}")
        print(f"   • No loadRecentInstances: {'✅' if no_recent_instances_function else '❌'}")
        
        return all([
            has_load_aws_data, has_update_accounts_table, 
            has_update_ec2_table, no_chart_functions
        ])
    
    return False

def test_quick_actions():
    print_header("TESTING QUICK ACTIONS SECTION")
    
    client = Client()
    
    response = client.get('/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check for quick action buttons
        has_ec2_button = 'EC2 Instances' in content and '/ec2-instances/' in content
        has_ssm_button = 'SSM Status' in content and '/ssm-status/' in content
        has_accounts_button = 'AWS Accounts' in content and '/accounts/' in content
        has_azure_button = 'Azure Portal' in content
        
        print("🚀 Quick Action Buttons:")
        print(f"   • EC2 Instances: {'✅' if has_ec2_button else '❌'}")
        print(f"   • SSM Status: {'✅' if has_ssm_button else '❌'}")
        print(f"   • AWS Accounts: {'✅' if has_accounts_button else '❌'}")
        print(f"   • Azure Portal: {'✅' if has_azure_button else '❌'}")
        
        return all([has_ec2_button, has_ssm_button, has_accounts_button, has_azure_button])
    
    return False

def main():
    try:
        print_header("NEW DASHBOARD LAYOUT TEST")
        
        print("🎨 Testing new dashboard design:")
        print("   • AWS section with accounts, EC2, and EKS tables")
        print("   • Azure section with subscriptions, VMs, and AKS tables")
        print("   • Removed refresh buttons and recent instances section")
        print("   • Quick actions section for navigation")
        
        # Run tests
        layout_ok = test_dashboard_layout()
        test_dashboard_data_structure()
        js_ok = test_dashboard_javascript()
        actions_ok = test_quick_actions()
        
        print_header("NEW DASHBOARD TEST SUMMARY")
        
        if layout_ok and js_ok and actions_ok:
            print("🎉 SUCCESS: New dashboard layout implemented successfully!")
            
            print("\n✅ Dashboard Features:")
            print("   • AWS section with proper branding and tables")
            print("   • Azure section with placeholder tables")
            print("   • Business Unit grouping for accounts and instances")
            print("   • Statistics by BU (Total, Running, SSM Online)")
            print("   • Quick actions for easy navigation")
            print("   • Removed refresh buttons and charts")
            print("   • Clean, organized two-section layout")
            
            print("\n📊 AWS Data Tables:")
            print("   • Accounts: BU | Account Count")
            print("   • EC2 Instances: BU | Total | Running | SSM Online")
            print("   • EKS Clusters: BU | Total | EC2 Nodes | Fargate (placeholder)")
            
            print("\n🔷 Azure Data Tables:")
            print("   • Subscriptions: BU | Subscription Count (placeholder)")
            print("   • Virtual Machines: BU | Total | Running | VM Agent (placeholder)")
            print("   • AKS Clusters: BU | Total | Public | Private (placeholder)")
            
            print("\n🚀 Ready for Use:")
            print("   1. Start server: python manage.py runserver")
            print("   2. Access dashboard: http://127.0.0.1:8000")
            print("   3. View AWS data organized by Business Unit")
            print("   4. Use quick actions for navigation")
            
        else:
            print("⚠️  WARNING: Some dashboard features may need attention")
            if not layout_ok:
                print("   • Dashboard layout issues detected")
            if not js_ok:
                print("   • JavaScript function issues detected")
            if not actions_ok:
                print("   • Quick actions issues detected")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
