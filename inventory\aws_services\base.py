"""
Base AWS service class with common functionality
"""
import boto3
import logging
from botocore.exceptions import ClientError, NoCredentialsError
from django.conf import settings


logger = logging.getLogger(__name__)


class AWSServiceBase:
    """Base class for AWS services with common functionality"""
    
    def __init__(self, account_id, region='us-east-1'):
        self.account_id = account_id
        self.region = region
        self.session = None
        self._clients = {}
        
    def get_session(self):
        """Get or create AWS session"""
        if not self.session:
            try:
                # Try to create session with default credentials
                self.session = boto3.Session(region_name=self.region)
                # Test the session
                sts = self.session.client('sts')
                sts.get_caller_identity()
                logger.info(f"AWS session created for region {self.region}")
            except (ClientError, NoCredentialsError) as e:
                logger.error(f"Failed to create AWS session: {e}")
                raise
        return self.session
    
    def get_client(self, service_name):
        """Get or create AWS service client"""
        if service_name not in self._clients:
            session = self.get_session()
            self._clients[service_name] = session.client(service_name)
        return self._clients[service_name]
    
    def handle_aws_error(self, error, operation):
        """Handle AWS API errors consistently"""
        error_code = error.response.get('Error', {}).get('Code', 'Unknown')
        error_message = error.response.get('Error', {}).get('Message', str(error))
        
        logger.error(f"AWS {operation} failed: {error_code} - {error_message}")
        
        # Return structured error info
        return {
            'error': True,
            'error_code': error_code,
            'error_message': error_message,
            'operation': operation
        }
    
    def paginate_results(self, client, operation, **kwargs):
        """Helper to paginate AWS API results"""
        try:
            paginator = client.get_paginator(operation)
            results = []
            
            for page in paginator.paginate(**kwargs):
                # The key varies by service, try common ones
                for key in ['Instances', 'Clusters', 'NodeGroups', 'FargateProfiles', 'Reservations']:
                    if key in page:
                        if key == 'Reservations':
                            # EC2 instances are nested in reservations
                            for reservation in page[key]:
                                results.extend(reservation.get('Instances', []))
                        else:
                            results.extend(page[key])
                        break
            
            return results
            
        except ClientError as e:
            logger.error(f"Pagination failed for {operation}: {e}")
            return []
    
    def get_tags_dict(self, tags_list):
        """Convert AWS tags list to dictionary"""
        if not tags_list:
            return {}
        
        return {tag.get('Key', ''): tag.get('Value', '') for tag in tags_list}
    
    def safe_get(self, data, *keys, default=None):
        """Safely get nested dictionary values"""
        for key in keys:
            if isinstance(data, dict) and key in data:
                data = data[key]
            else:
                return default
        return data
    
    def format_datetime(self, dt):
        """Format datetime for database storage"""
        if dt:
            return dt.replace(tzinfo=None) if dt.tzinfo else dt
        return None
