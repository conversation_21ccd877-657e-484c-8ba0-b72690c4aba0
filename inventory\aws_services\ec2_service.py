"""
EC2 service for AWS inventory operations
Handles EC2 instances, SSM status, and related operations
"""
import json
import logging
from datetime import datetime
from botocore.exceptions import BotoCoreError, ClientError
from .base import AWSServiceBase


logger = logging.getLogger(__name__)


class EC2Service(AWSServiceBase):
    """Service for EC2 inventory operations"""
    
    def __init__(self, account_id, region='us-east-1'):
        super().__init__(account_id, region)
        self._ec2_client = None
        self._ssm_client = None
    
    @property
    def ec2_client(self):
        """Get EC2 client"""
        if not self._ec2_client:
            self._ec2_client = self.get_client('ec2')
        return self._ec2_client
    
    @property
    def ssm_client(self):
        """Get SSM client"""
        if not self._ssm_client:
            self._ssm_client = self.get_client('ssm')
        return self._ssm_client
    
    def get_instances(self):
        """Get all EC2 instances for the account"""
        instances_data = []
        
        try:
            instances = self.paginate_results(self.ec2_client, 'describe_instances')
            
            for instance in instances:
                instance_data = self._process_instance(instance)
                if instance_data:
                    instances_data.append(instance_data)
        
        except (BotoCoreError, ClientError) as e:
            logger.error(f"Error getting instances for account {self.account_id}: {str(e)}")
            return self.handle_aws_error(e, 'get_instances')
        
        return instances_data
    
    def _process_instance(self, instance):
        """Process individual EC2 instance data with all required fields"""
        try:
            instance_id = instance['InstanceId']
            instance_type = instance['InstanceType']
            state = instance['State']['Name']

            # Extract tags
            tags = instance.get('Tags', [])
            tags_dict = self.get_tags_dict(tags)
            tags_str = ", ".join([f"{tag['Key']}={tag['Value']}" for tag in tags])

            # Get instance type details
            instance_specs = self._get_instance_specs(instance_type)

            # Get storage information
            storage_info = self._get_storage_info(instance)

            # Get network and security information
            network_info = self._get_network_info(instance)

            # Get OS information from SSM
            os_info = self._get_os_info(instance_id)

            # Process tags for flattening
            flattened_tags = []
            for tag in tags:
                flattened_tags.append({
                    'key': tag['Key'],
                    'value': tag['Value']
                })

            return {
                # Core identification
                'instance_id': instance_id,
                'name': tags_dict.get('Name', ''),
                'state': state,
                'instance_type': instance_type,
                'vcpu_count': instance_specs.get('vcpu_count'),
                'memory_gb': instance_specs.get('memory_gb'),

                # Network information
                'private_ip_address': instance.get('PrivateIpAddress'),
                'private_dns_name': instance.get('PrivateDnsName', ''),
                'subnet_id': network_info.get('subnet_id', ''),
                'subnet_name': network_info.get('subnet_name', ''),
                'security_groups': network_info.get('security_groups', ''),

                # Infrastructure details
                'ami_id': instance.get('ImageId', ''),
                'instance_profile': network_info.get('instance_profile', ''),

                # Storage information
                'root_volume_id': storage_info.get('root_volume_id', ''),
                'root_volume_size_gb': storage_info.get('root_volume_size_gb'),
                'data_disk_count': storage_info.get('data_disk_count', 0),

                # OS information
                'os_information': os_info,
                'platform_type': instance.get('PlatformDetails', ''),

                # Tag fields (case-insensitive lookup)
                'env_tag': self._get_tag_value(tags_dict, ['env', 'Env', 'environment', 'Environment']),
                'uai_tag': self._get_tag_value(tags_dict, ['uai', 'UAI']),
                'patch_tag': self._get_tag_value(tags_dict, ['patch', 'Patch']),
                'app_env_cfg_id': self._get_tag_value(tags_dict, ['AppEnvCfgID', 'appenvCfgid']),
                'maintenance_schedule': self._get_tag_value(tags_dict, ['maintenanceschedule', 'MaintenanceSchedule']),
                'schedule_tag': self._get_tag_value(tags_dict, ['schedule', 'Schedule']),
                'backup_tag': self._get_tag_value(tags_dict, ['backup', 'Backup']),
                'cto_cloud_ops_managed': self._get_tag_value(tags_dict, ['ctocloudopsmanaged', 'CTOCloudOpsManaged']),
                'patch_provider': self._get_tag_value(tags_dict, ['patchprovider', 'PatchProvider']),
                'patch_group': self._get_tag_value(tags_dict, ['patchgroup', 'PatchGroup']),
                'patch_exempt': self._get_tag_value(tags_dict, ['patchexempt', 'PatchExempt']),

                # Raw tags data
                'all_tags_raw': tags_str,
                'flattened_tags': flattened_tags,
            }

        except Exception as e:
            logger.error(f"Error processing instance {instance.get('InstanceId', 'unknown')}: {str(e)}")
            return None

    def _get_tag_value(self, tags_dict, possible_keys):
        """Get tag value with case-insensitive key matching"""
        for key in possible_keys:
            if key in tags_dict:
                return tags_dict[key]
        return ''
    
    def _get_instance_specs(self, instance_type):
        """Get instance specifications"""
        try:
            response = self.ec2_client.describe_instance_types(InstanceTypes=[instance_type])
            instance_info = response["InstanceTypes"][0]
            
            vcpu_count = instance_info["VCpuInfo"].get("DefaultVCpus")
            memory_gb = round(instance_info["MemoryInfo"].get("SizeInMiB", 0) / 1024, 2)
            
            return {
                'vcpu_count': vcpu_count,
                'memory_gb': memory_gb
            }
        except Exception as e:
            logger.error(f"Error getting instance specs for {instance_type}: {str(e)}")
            return {'vcpu_count': None, 'memory_gb': None}
    
    def _get_network_info(self, instance):
        """Get network and security information for instance"""
        try:
            # Get subnet information
            subnet_id = ''
            subnet_name = ''
            if 'SubnetId' in instance:
                subnet_id = instance['SubnetId']
                # Try to get subnet name from tags
                try:
                    subnet_response = self.ec2_client.describe_subnets(SubnetIds=[subnet_id])
                    if subnet_response['Subnets']:
                        subnet = subnet_response['Subnets'][0]
                        subnet_tags = subnet.get('Tags', [])
                        for tag in subnet_tags:
                            if tag['Key'] == 'Name':
                                subnet_name = tag['Value']
                                break
                except Exception:
                    pass

            # Get security groups
            security_groups = []
            for sg in instance.get('SecurityGroups', []):
                security_groups.append({
                    'id': sg['GroupId'],
                    'name': sg['GroupName']
                })

            # Get instance profile
            instance_profile = ''
            if 'IamInstanceProfile' in instance:
                instance_profile = instance['IamInstanceProfile'].get('Arn', '')

            return {
                'subnet_id': subnet_id,
                'subnet_name': subnet_name,
                'security_groups': json.dumps(security_groups) if security_groups else '',
                'instance_profile': instance_profile
            }
        except Exception as e:
            logger.error(f"Error getting network info: {str(e)}")
            return {'subnet_id': '', 'subnet_name': '', 'security_groups': '', 'instance_profile': ''}

    def _get_storage_info(self, instance):
        """Get storage information for instance"""
        try:
            root_device_name = instance.get('RootDeviceName')
            block_devices = instance.get('BlockDeviceMappings', [])

            root_volume_id = None
            root_volume_size_gb = None
            data_disk_count = 0

            for device in block_devices:
                if device['DeviceName'] == root_device_name:
                    volume_id = device['Ebs']['VolumeId']
                    volume_response = self.ec2_client.describe_volumes(VolumeIds=[volume_id])
                    root_volume_id = volume_id
                    root_volume_size_gb = volume_response['Volumes'][0]['Size']
                else:
                    data_disk_count += 1

            return {
                'root_volume_id': root_volume_id,
                'root_volume_size_gb': root_volume_size_gb,
                'data_disk_count': data_disk_count
            }
        except Exception as e:
            logger.error(f"Error getting storage info: {str(e)}")
            return {'root_volume_id': None, 'root_volume_size_gb': None, 'data_disk_count': 0}
    
    def _get_os_info(self, instance_id):
        """Get OS information from SSM"""
        try:
            response = self.ssm_client.describe_instance_information(
                InstanceInformationFilterList=[{
                    'key': 'InstanceIds',
                    'valueSet': [instance_id]
                }]
            )
            
            if not response['InstanceInformationList']:
                return 'No_SSM_Response'
            
            os_info = response['InstanceInformationList'][0]
            platform_type = os_info.get('PlatformType', 'Unknown')
            os_name = os_info.get('PlatformName', 'No_SSMAgent_Data')
            os_version = os_info.get('PlatformVersion', '')
            
            return f"{os_name}_{os_version}"
        
        except Exception as e:
            logger.error(f"Error getting OS info for {instance_id}: {str(e)}")
            return 'No_SSM_Response'

    def get_instance_ssm_status(self, instance_id):
        """Get SSM status for a specific instance"""
        try:
            response = self.ssm_client.describe_instance_information(
                InstanceInformationFilterList=[{
                    'key': 'InstanceIds',
                    'valueSet': [instance_id]
                }]
            )
            
            if not response['InstanceInformationList']:
                return {
                    'ping_status': 'Inactive',
                    'agent_version': 'N/A',
                    'is_latest_version': None,
                    'last_ping_datetime': None,
                    'platform_name': 'N/A',
                    'platform_type': 'N/A',
                    'ssm_configured': False
                }
            
            instance_info = response['InstanceInformationList'][0]
            
            # Process last ping datetime
            last_ping = instance_info.get('LastPingDateTime')
            if last_ping:
                last_ping = self.format_datetime(last_ping)
            
            return {
                'ping_status': instance_info.get('PingStatus', 'Unknown'),
                'agent_version': instance_info.get('AgentVersion', 'N/A'),
                'is_latest_version': instance_info.get('IsLatestVersion'),
                'last_ping_datetime': last_ping,
                'platform_name': instance_info.get('PlatformName', 'N/A'),
                'platform_type': instance_info.get('PlatformType', 'N/A'),
                'ssm_configured': True
            }
        
        except Exception as e:
            logger.error(f"Error getting SSM status for {instance_id}: {str(e)}")
            return None
