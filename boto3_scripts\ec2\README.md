# EC2 Scripts

This directory contains scripts for EC2 instance management and inventory collection.

## Scripts

### `ec2_inventory.py`
Comprehensive EC2 instance inventory collection script.

**Features:**
- Collects detailed instance information including specifications, network configuration, and storage
- Extracts and processes all instance tags
- Retrieves instance type specifications (vCPU, memory)
- Gathers EBS volume information
- Processes security groups and network details
- Generates Excel reports with all collected data

**Output Fields:**
- Business Unit, Account Name, Account ID, Region
- Instance ID, State, Name, OS Information
- Private IP Address, Instance Type, vCPU, Memory
- Root EBS Volume details, Data Disk Count
- All AWS tags (Env, UAI, Patch, etc.)
- Security Groups, Subnet information

**Usage:**
```bash
python ec2_inventory.py
```

**Requirements:**
- AWS profiles configured for target accounts
- IAM permissions for EC2 and IAM services
- Python dependencies: boto3, pandas, openpyxl

**Configuration:**
- Reads account information from roles configuration file
- Uses AWS profiles in format: `support-{account_id}`
- Supports multi-threading for faster processing
