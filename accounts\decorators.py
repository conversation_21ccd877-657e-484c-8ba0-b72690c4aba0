from functools import wraps
from django.contrib.auth.decorators import user_passes_test
from django.contrib import messages
from django.shortcuts import redirect
from django.http import HttpResponseForbidden, JsonResponse
from django.core.exceptions import PermissionDenied


def role_required(role_name, redirect_url='inventory:dashboard', message=None):
    """
    Decorator to require specific user role
    
    Args:
        role_name: Required role name ('admin', 'automation_user', 'reader')
        redirect_url: URL to redirect to if permission denied
        message: Custom message to display
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('accounts:login')
            
            if not request.user.has_role(role_name) and not request.user.is_superuser:
                if message:
                    messages.error(request, message)
                else:
                    messages.error(
                        request, 
                        f'You need {role_name.replace("_", " ").title()} role to access this page.'
                    )
                return redirect(redirect_url)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def admin_required(view_func=None, *, redirect_url='inventory:dashboard', message=None):
    """
    Decorator to require admin role
    """
    def decorator(func):
        return role_required('admin', redirect_url, message)(func)
    
    if view_func is None:
        return decorator
    else:
        return decorator(view_func)


def automation_user_required(view_func=None, *, redirect_url='inventory:dashboard', message=None):
    """
    Decorator to require automation user role or higher
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('accounts:login')
            
            if not request.user.can_execute_automation():
                if message:
                    messages.error(request, message)
                else:
                    messages.error(
                        request, 
                        'You need Automation User or Admin role to access this page.'
                    )
                return redirect(redirect_url)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    
    if view_func is None:
        return decorator
    else:
        return decorator(view_func)


def approved_user_required(view_func=None, *, redirect_url='accounts:login', message=None):
    """
    Decorator to require approved user account
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('accounts:login')
            
            if not request.user.is_approved and not request.user.is_superuser:
                if message:
                    messages.error(request, message)
                else:
                    messages.error(
                        request, 
                        'Your account is pending approval. Please contact an administrator.'
                    )
                return redirect(redirect_url)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    
    if view_func is None:
        return decorator
    else:
        return decorator(view_func)


def ajax_role_required(role_name):
    """
    Decorator for AJAX views that require specific role
    Returns JSON error response instead of redirect
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'error': 'Authentication required',
                    'redirect': '/accounts/login/'
                }, status=401)
            
            if not request.user.has_role(role_name) and not request.user.is_superuser:
                return JsonResponse({
                    'error': f'You need {role_name.replace("_", " ").title()} role to access this resource.'
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def ajax_admin_required(view_func):
    """AJAX decorator for admin-only views"""
    return ajax_role_required('admin')(view_func)


def ajax_automation_user_required(view_func):
    """AJAX decorator for automation user or admin views"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return JsonResponse({
                'error': 'Authentication required',
                'redirect': '/accounts/login/'
            }, status=401)
        
        if not request.user.can_execute_automation():
            return JsonResponse({
                'error': 'You need Automation User or Admin role to access this resource.'
            }, status=403)
        
        return view_func(request, *args, **kwargs)
    return wrapper


class RoleRequiredMixin:
    """
    Mixin for class-based views to require specific role
    """
    required_role = None
    permission_denied_message = None
    redirect_url = 'inventory:dashboard'

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts:login')
        
        if self.required_role and not request.user.has_role(self.required_role) and not request.user.is_superuser:
            if self.permission_denied_message:
                messages.error(request, self.permission_denied_message)
            else:
                messages.error(
                    request, 
                    f'You need {self.required_role.replace("_", " ").title()} role to access this page.'
                )
            return redirect(self.redirect_url)
        
        return super().dispatch(request, *args, **kwargs)


class AdminRequiredMixin(RoleRequiredMixin):
    """Mixin for admin-only views"""
    required_role = 'admin'


class AutomationUserRequiredMixin:
    """Mixin for automation user or admin views"""
    permission_denied_message = None
    redirect_url = 'inventory:dashboard'

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts:login')
        
        if not request.user.can_execute_automation():
            if self.permission_denied_message:
                messages.error(request, self.permission_denied_message)
            else:
                messages.error(
                    request, 
                    'You need Automation User or Admin role to access this page.'
                )
            return redirect(self.redirect_url)
        
        return super().dispatch(request, *args, **kwargs)


class ApprovedUserRequiredMixin:
    """Mixin to require approved user account"""
    permission_denied_message = None
    redirect_url = 'accounts:login'

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('accounts:login')
        
        if not request.user.is_approved and not request.user.is_superuser:
            if self.permission_denied_message:
                messages.error(request, self.permission_denied_message)
            else:
                messages.error(
                    request, 
                    'Your account is pending approval. Please contact an administrator.'
                )
            return redirect(self.redirect_url)
        
        return super().dispatch(request, *args, **kwargs)


# Utility functions for template use
def user_has_role(user, role_name):
    """Check if user has specific role - for use in templates"""
    return user.is_authenticated and (user.has_role(role_name) or user.is_superuser)


def user_can_execute_automation(user):
    """Check if user can execute automation - for use in templates"""
    return user.is_authenticated and user.can_execute_automation()


def user_can_manage_users(user):
    """Check if user can manage users - for use in templates"""
    return user.is_authenticated and user.can_manage_users()


def user_is_approved(user):
    """Check if user is approved - for use in templates"""
    return user.is_authenticated and (user.is_approved or user.is_superuser)
