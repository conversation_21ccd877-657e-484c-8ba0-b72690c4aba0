# EKS Scripts

This directory contains scripts for Amazon EKS cluster management and inventory collection.

## Scripts

### `eks_inventory.py`
Comprehensive EKS cluster inventory collection script.

**Features:**
- Collects detailed EKS cluster information
- Retrieves node group configurations and scaling settings
- Gathers Fargate profile information
- Processes cluster networking and security configurations
- Extracts cluster addons and logging configurations
- Generates Excel reports with all collected data

**Output Fields:**
- Business Unit, Account Name, Account ID, Region
- Cluster Name, ARN, Kubernetes Version, Platform Version
- Cluster Status, Type (EC2/Fargate/Hybrid)
- Network configuration (VPC, Subnets, Security Groups)
- Node Group details (instance types, scaling configuration)
- Fargate Profile information
- Cluster addons and logging settings
- All cluster and node group tags

**Usage:**
```bash
python eks_inventory.py
```

**Requirements:**
- AWS profiles configured for target accounts
- IAM permissions for EKS service
- Python dependencies: boto3, pandas, openpyxl

**Configuration:**
- Reads account information from roles configuration file
- Uses AWS profiles in format: `support-{account_id}`
- Supports multi-threading for faster processing

**Note:**
This script provides comprehensive EKS inventory data that can be used for:
- Cluster capacity planning
- Security compliance auditing
- Cost optimization analysis
- Infrastructure documentation
