#!/usr/bin/env python
"""
Fix custom user model migration issues
"""
import os
import sys
import shutil
from pathlib import Path

def print_header(title):
    print("\n" + "="*70)
    print(f" {title}")
    print("="*70)

def print_section(title):
    print(f"\n--- {title} ---")

def backup_database():
    """Backup existing database"""
    print_section("Backing up database")
    
    if os.path.exists('db.sqlite3'):
        backup_name = f'db_backup_{int(os.path.getmtime("db.sqlite3"))}.sqlite3'
        shutil.copy2('db.sqlite3', backup_name)
        print(f"✅ Database backed up to: {backup_name}")
        return backup_name
    else:
        print("ℹ️  No existing database found")
        return None

def clean_migration_files():
    """Clean up migration files"""
    print_section("Cleaning migration files")
    
    # Remove all migration files except __init__.py
    for app in ['accounts', 'inventory']:
        migrations_dir = Path(f'{app}/migrations')
        if migrations_dir.exists():
            for file in migrations_dir.glob('*.py'):
                if file.name != '__init__.py':
                    file.unlink()
                    print(f"✅ Removed: {file}")
            
            # Remove cache
            cache_dir = migrations_dir / '__pycache__'
            if cache_dir.exists():
                shutil.rmtree(cache_dir)
                print(f"✅ Removed cache: {cache_dir}")

def remove_database():
    """Remove existing database"""
    print_section("Removing existing database")
    
    if os.path.exists('db.sqlite3'):
        os.remove('db.sqlite3')
        print("✅ Removed existing database")
    else:
        print("ℹ️  No database to remove")

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔧 {description}")
    print(f"   Command: {command}")
    
    result = os.system(command)
    if result == 0:
        print(f"   ✅ Success")
        return True
    else:
        print(f"   ❌ Failed with exit code: {result}")
        return False

def create_fresh_migrations():
    """Create fresh migrations"""
    print_section("Creating fresh migrations")
    
    # Create accounts migrations first
    if not run_command("python manage.py makemigrations accounts", "Creating accounts migrations"):
        return False
    
    # Create inventory migrations
    if not run_command("python manage.py makemigrations inventory", "Creating inventory migrations"):
        return False
    
    # Run all migrations
    if not run_command("python manage.py migrate", "Running all migrations"):
        return False
    
    return True

def setup_user_system():
    """Set up the user system"""
    print_section("Setting up user system")
    
    try:
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
        import django
        django.setup()
        
        from django.contrib.auth import get_user_model
        from accounts.models import UserRole
        from django.utils import timezone
        
        User = get_user_model()
        
        # Create default roles
        roles_data = [
            {
                'name': 'admin',
                'display_name': 'Admin',
                'description': 'Full access to all features including user management, system configuration, and all operations.'
            },
            {
                'name': 'automation_user',
                'display_name': 'Automation User',
                'description': 'Read access to all inventory and reports, plus execute access for deployment tasks and automation workflows.'
            },
            {
                'name': 'reader',
                'display_name': 'Reader',
                'description': 'Read-only access to inventory, automation reports, and decommission reports.'
            }
        ]
        
        for role_data in roles_data:
            role, created = UserRole.objects.get_or_create(
                name=role_data['name'],
                defaults=role_data
            )
            if created:
                print(f"✅ Created role: {role.display_name}")
            else:
                print(f"ℹ️  Role already exists: {role.display_name}")
        
        # Create admin user
        admin_role = UserRole.objects.get(name='admin')

        if not User.objects.filter(username='admin').exists():
            admin_user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='System',
                last_name='Administrator',
                is_staff=True,
                is_superuser=True,
                is_active=True
            )

            # Set custom fields after creation
            admin_user.is_approved = True
            admin_user.user_role = admin_role
            admin_user.approved_at = timezone.now()
            admin_user.save()

            print(f"✅ Created admin user: admin / admin123")
        else:
            print("ℹ️  Admin user already exists")
        
        return True
        
    except Exception as e:
        print(f"❌ User system setup failed: {e}")
        return False

def test_system():
    """Test the system"""
    print_section("Testing system")
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
        import django
        django.setup()
        
        from django.test import Client
        from django.contrib.auth import get_user_model
        from accounts.models import UserRole
        
        User = get_user_model()
        
        # Test models
        role_count = UserRole.objects.count()
        user_count = User.objects.count()
        
        print(f"✅ UserRole model: {role_count} roles")
        print(f"✅ CustomUser model: {user_count} users")
        
        # Test authentication
        client = Client()
        
        # Test login page
        response = client.get('/accounts/login/')
        if response.status_code == 200:
            print("✅ Login page accessible")
        else:
            print(f"⚠️  Login page status: {response.status_code}")
        
        # Test admin login
        login_success = client.login(username='admin', password='admin123')
        if login_success:
            print("✅ Admin login successful")
            
            # Test protected page access
            response = client.get('/')
            if response.status_code == 200:
                print("✅ Dashboard accessible after login")
            else:
                print(f"⚠️  Dashboard status: {response.status_code}")
        else:
            print("❌ Admin login failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def main():
    print_header("CUSTOM USER MODEL MIGRATION FIX")
    
    print("🚨 Fixing custom user model migration conflicts")
    print("🔧 This will create a fresh database with proper custom user model")
    
    response = input("\n❓ Proceed with migration fix? This will delete existing data. (y/N): ")
    
    if response.lower() not in ['y', 'yes']:
        print("ℹ️  Migration fix cancelled")
        return
    
    print_header("STARTING MIGRATION FIX")
    
    # Step 1: Backup database
    backup_file = backup_database()
    
    # Step 2: Clean migration files
    clean_migration_files()
    
    # Step 3: Remove database
    remove_database()
    
    # Step 4: Create fresh migrations
    if not create_fresh_migrations():
        print("❌ Migration creation failed")
        return
    
    # Step 5: Setup user system
    if not setup_user_system():
        print("❌ User system setup failed")
        return
    
    # Step 6: Test system
    if not test_system():
        print("❌ System test failed")
        return
    
    print_header("MIGRATION FIX SUCCESSFUL!")
    
    print("🎉 Custom user model migration fix completed successfully!")
    
    print_section("✅ What was fixed")
    print("   • Resolved reverse accessor conflicts")
    print("   • Created fresh database with custom user model")
    print("   • Set up default roles (Admin, Automation User, Reader)")
    print("   • Created admin user with full permissions")
    print("   • Verified authentication system")
    
    print_section("🚀 System is ready")
    print("   1. Start server: python manage.py runserver")
    print("   2. Access login: http://127.0.0.1:8000/accounts/login/")
    print("   3. Login with: admin / admin123")
    print("   4. Access user management: Admin interface")
    print("   5. Test registration: http://127.0.0.1:8000/accounts/register/")
    
    print_section("🔐 Full features now available")
    print("   ✅ Role-based access control")
    print("   ✅ User approval workflow")
    print("   ✅ Session tracking")
    print("   ✅ Activity logging")
    print("   ✅ Account lockout protection")
    print("   ✅ Extended user fields")
    print("   ✅ SAML SSO support prepared")
    
    print_section("👥 User roles configured")
    print("   🔑 Admin: Full system access + user management")
    print("   ⚡ Automation User: Read access + deployment execution")
    print("   👁️  Reader: Read-only access to inventory and reports")
    
    print("\n" + "="*70)
    print(" 🎯 CUSTOM USER MODEL SUCCESSFULLY CONFIGURED!")
    print("="*70)

if __name__ == '__main__':
    main()
