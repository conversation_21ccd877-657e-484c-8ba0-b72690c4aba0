#!/usr/bin/env python
"""
Simple migration fix - temporarily disable custom user model
"""
import os
import sys

def print_header(title):
    print("\n" + "="*70)
    print(f" {title}")
    print("="*70)

def print_section(title):
    print(f"\n--- {title} ---")

def disable_custom_user_model():
    """Temporarily disable custom user model to fix migration issues"""
    print_section("Disabling Custom User Model Temporarily")
    
    settings_file = 'aws_inventory/settings.py'
    
    try:
        with open(settings_file, 'r') as f:
            content = f.read()
        
        # Comment out the custom user model
        updated_content = content.replace(
            "AUTH_USER_MODEL = 'accounts.CustomUser'",
            "# AUTH_USER_MODEL = 'accounts.CustomUser'  # Temporarily disabled for migration fix"
        )
        
        with open(settings_file, 'w') as f:
            f.write(updated_content)
        
        print("✅ Custom user model temporarily disabled")
        return True
        
    except Exception as e:
        print(f"❌ Failed to modify settings: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without custom user model"""
    print_section("Testing Basic Functionality")
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
        import django
        django.setup()
        
        from django.test import Client
        
        client = Client()
        
        # Test login page
        response = client.get('/accounts/login/')
        if response.status_code == 200:
            print("✅ Login page accessible")
        else:
            print(f"⚠️  Login page status: {response.status_code}")
        
        # Test registration page
        response = client.get('/accounts/register/')
        if response.status_code == 200:
            print("✅ Registration page accessible")
        else:
            print(f"⚠️  Registration page status: {response.status_code}")
        
        # Test protected pages redirect
        response = client.get('/')
        if response.status_code in [302, 200]:
            print("✅ Protected pages handling works")
        else:
            print(f"⚠️  Protected page status: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def create_simple_user_demo():
    """Create a simple demo showing the user management features"""
    print_section("Creating User Management Demo")
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
        import django
        django.setup()
        
        from django.contrib.auth.models import User
        from django.test import Client
        
        # Create a test admin user with Django's default User model
        if not User.objects.filter(username='admin').exists():
            admin_user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                is_staff=True,
                is_superuser=True
            )
            print("✅ Created admin user: admin / admin123")
        else:
            print("ℹ️  Admin user already exists")
        
        # Test login functionality
        client = Client()
        login_success = client.login(username='admin', password='admin123')
        if login_success:
            print("✅ Admin login test successful")
        else:
            print("⚠️  Admin login test failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo creation failed: {e}")
        return False

def show_workaround_instructions():
    """Show instructions for using the system with workaround"""
    print_section("Workaround Instructions")
    
    print("📋 Current Status:")
    print("   • Custom user model temporarily disabled")
    print("   • Using Django's default User model")
    print("   • Login and registration pages working")
    print("   • Basic authentication functional")
    
    print("\n🔧 To use the system:")
    print("   1. Start server: python manage.py runserver")
    print("   2. Access login: http://127.0.0.1:8000/accounts/login/")
    print("   3. Login with: admin / admin123")
    print("   4. Access inventory pages (now protected)")
    
    print("\n🎯 User Management Features Available:")
    print("   ✅ Login/logout functionality")
    print("   ✅ User registration")
    print("   ✅ Protected page access")
    print("   ✅ Admin interface")
    print("   ✅ Modern UI design")
    
    print("\n🔮 Full Custom User Model Features:")
    print("   • Role-based access control")
    print("   • Session tracking")
    print("   • Activity logging")
    print("   • Account approval workflow")
    print("   • SAML SSO support")
    print("   (Available when migration issues resolved)")

def enable_custom_user_model():
    """Re-enable custom user model"""
    print_section("Re-enabling Custom User Model")
    
    settings_file = 'aws_inventory/settings.py'
    
    try:
        with open(settings_file, 'r') as f:
            content = f.read()
        
        # Re-enable the custom user model
        updated_content = content.replace(
            "# AUTH_USER_MODEL = 'accounts.CustomUser'  # Temporarily disabled for migration fix",
            "AUTH_USER_MODEL = 'accounts.CustomUser'"
        )
        
        with open(settings_file, 'w') as f:
            f.write(updated_content)
        
        print("✅ Custom user model re-enabled")
        print("⚠️  You'll need to resolve migration issues to use full features")
        return True
        
    except Exception as e:
        print(f"❌ Failed to modify settings: {e}")
        return False

def main():
    print_header("SIMPLE MIGRATION FIX")
    
    print("🚨 Migration issue detected with custom user model")
    print("🔧 This script provides a workaround to get basic functionality working")
    
    print("\n📋 Options:")
    print("   1. Temporarily disable custom user model (recommended)")
    print("   2. Keep custom user model and show migration instructions")
    print("   3. Exit and resolve manually")
    
    choice = input("\n❓ Choose option (1/2/3): ").strip()
    
    if choice == '1':
        print_header("APPLYING WORKAROUND")
        
        if disable_custom_user_model():
            if test_basic_functionality():
                if create_simple_user_demo():
                    print_header("WORKAROUND SUCCESSFUL")
                    
                    print("🎉 Basic user management is now working!")
                    
                    show_workaround_instructions()
                    
                    print_section("Next Steps")
                    print("1. Test the login system: http://127.0.0.1:8000/accounts/login/")
                    print("2. Use admin credentials: admin / admin123")
                    print("3. Verify inventory pages are protected")
                    print("4. When ready, resolve migration issues for full features")
                    
                    restore = input("\n❓ Re-enable custom user model for future? (y/N): ")
                    if restore.lower() in ['y', 'yes']:
                        enable_custom_user_model()
                        print("\n✅ Custom user model re-enabled for future use")
                        print("⚠️  Remember to resolve migration issues later")
                else:
                    print("❌ Demo creation failed")
            else:
                print("❌ Basic functionality test failed")
        else:
            print("❌ Failed to apply workaround")
    
    elif choice == '2':
        print_header("MIGRATION RESOLUTION INSTRUCTIONS")
        
        print("🔧 To resolve migration issues manually:")
        print("   1. Stop any running Django processes")
        print("   2. Delete db.sqlite3 file")
        print("   3. Delete accounts/migrations/*.py (except __init__.py)")
        print("   4. Run: python manage.py makemigrations accounts")
        print("   5. Run: python manage.py migrate")
        print("   6. Run: python manage.py setup_user_system")
        
        print("\n⚠️  This will create a fresh database")
        print("💾 Backup any important data first")
    
    elif choice == '3':
        print("ℹ️  Exiting - resolve migration issues manually")
    
    else:
        print("❌ Invalid choice")

if __name__ == '__main__':
    main()
