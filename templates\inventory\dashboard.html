{% extends 'inventory/base.html' %}

{% block title %}Cloud Operations Dashboard - Enterprise Central{% endblock %}
{% block page_title %}Enterprise Cloud Operations Dashboard{% endblock %}

{% block page_actions %}
<!-- No refresh button on dashboard -->
{% endblock %}

{% block page_header %}
<!-- No page header on dashboard -->
{% endblock %}

{% block extra_css %}
<style>
    /* Modern Enterprise Dashboard Styles */
    .main-content {
        height: calc(100vh - var(--header-height) - var(--footer-height));
        overflow: hidden;
        padding: 20px;
        margin-top: 0 !important;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }

    .main-content .page-header {
        display: none !important;
    }

    .dashboard-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding-top: 50px;
    }

    /* Enterprise Dashboard Header */
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 24px 32px;
        color: white;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        margin-bottom: 24px;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 8px 0 0 0;
        position: relative;
        z-index: 1;
    }

    .dashboard-stats-overview {
        display: flex;
        gap: 32px;
        margin-top: 20px;
        position: relative;
        z-index: 1;
    }

    .overview-stat {
        text-align: center;
    }

    .overview-stat-number {
        font-size: 2rem;
        font-weight: 700;
        display: block;
    }

    .overview-stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Modern Metrics Cards */
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin-bottom: 32px;
    }

    .metric-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--accent-color), #5dade2);
    }

    .metric-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
    }

    .metric-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .metric-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .metric-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 8px;
        line-height: 1;
    }

    .metric-change {
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .metric-change.positive {
        color: #10b981;
    }

    .metric-change.negative {
        color: #ef4444;
    }

    .metric-change.neutral {
        color: #64748b;
    }

    /* Cloud Sections */
    .cloud-sections {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 32px;
        flex: 1;
    }

    .cloud-section {
        background: white;
        border-radius: 20px;
        padding: 0;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .cloud-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
    }

    .section-header {
        padding: 24px 32px;
        color: white;
        font-size: 1.3rem;
        font-weight: 700;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .section-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    }

    .section-header .cloud-icon {
        font-size: 1.5rem;
        margin-right: 12px;
        position: relative;
        z-index: 1;
    }

    .section-header .cloud-name {
        position: relative;
        z-index: 1;
    }

    .aws-header {
        background: linear-gradient(135deg, #FF9900 0%, #FF6600 100%);
    }

    .azure-header {
        background: linear-gradient(135deg, #0078D4 0%, #005A9E 100%);
    }

    .section-content {
        padding: 32px;
    }

    /* Data Visualization */
    .data-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
        margin-bottom: 24px;
    }

    .data-widget {
        background: #f8fafc;
        border-radius: 16px;
        padding: 24px;
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .data-widget:hover {
        background: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
    }

    .widget-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .widget-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .widget-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
    }

    /* Business Unit Bars */
    .bu-chart {
        margin-bottom: 16px;
    }

    .bu-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        padding: 12px 16px;
        background: white;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        transition: all 0.2s ease;
    }

    .bu-item:hover {
        border-color: var(--accent-color);
        box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
    }

    .bu-name {
        font-weight: 600;
        color: #374151;
        flex: 1;
    }

    .bu-metrics {
        display: flex;
        gap: 16px;
        align-items: center;
    }

    .bu-metric {
        text-align: center;
    }

    .bu-metric-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #1e293b;
        display: block;
    }

    .bu-metric-label {
        font-size: 0.75rem;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Status Indicators */
    .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .status-indicator.healthy {
        background: #dcfce7;
        color: #166534;
    }

    .status-indicator.warning {
        background: #fef3c7;
        color: #92400e;
    }

    .status-indicator.critical {
        background: #fee2e2;
        color: #991b1b;
    }

    .status-indicator.info {
        background: #dbeafe;
        color: #1e40af;
    }

    /* Progress Bars */
    .progress-bar-container {
        background: #f1f5f9;
        border-radius: 8px;
        height: 8px;
        overflow: hidden;
        margin: 8px 0;
    }

    .progress-bar {
        height: 100%;
        border-radius: 8px;
        transition: width 0.3s ease;
    }

    .progress-bar.success {
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .progress-bar.warning {
        background: linear-gradient(90deg, #f59e0b, #d97706);
    }

    .progress-bar.danger {
        background: linear-gradient(90deg, #ef4444, #dc2626);
    }

    /* Quick Actions */
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-top: 32px;
    }

    .action-card {
        background: white;
        border-radius: 16px;
        padding: 20px;
        text-align: center;
        text-decoration: none;
        color: inherit;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 1px solid #e2e8f0;
    }

    .action-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 40px rgba(0, 0, 0, 0.15);
        text-decoration: none;
        color: inherit;
    }

    .action-icon {
        width: 48px;
        height: 48px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin: 0 auto 12px;
    }

    .action-title {
        font-size: 1rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 4px;
    }

    .action-description {
        font-size: 0.85rem;
        color: #64748b;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .cloud-sections {
            grid-template-columns: 1fr;
            gap: 24px;
        }

        .metrics-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .dashboard-stats-overview {
            gap: 20px;
        }
    }

    @media (max-width: 768px) {
        .main-content {
            padding: 16px;
        }

        .dashboard-container {
            padding-top: 20px;
            gap: 16px;
        }

        .dashboard-header {
            padding: 20px 24px;
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .dashboard-stats-overview {
            flex-direction: column;
            gap: 16px;
        }

        .metrics-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .metric-card {
            padding: 20px;
        }

        .section-content {
            padding: 24px;
        }

        .data-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .quick-actions {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
        }
    }

    @media (max-width: 480px) {
        .dashboard-header {
            padding: 16px 20px;
        }

        .dashboard-title {
            font-size: 1.5rem;
        }

        .section-content {
            padding: 20px;
        }

        .metric-card {
            padding: 16px;
        }

        .data-widget {
            padding: 20px;
        }
    }

    /* Animation Keyframes */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    .animate-fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    .animate-pulse {
        animation: pulse 2s infinite;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .tables-row {
            flex-direction: column;
            gap: 6px;
        }

        .table-container {
            min-height: 120px;
        }

        .actions-grid {
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
        }

        .quick-actions {
            height: 70px;
        }
    }

    @media (max-width: 576px) {
        .main-content {
            padding: 4px;
        }

        .dashboard-container {
            gap: 4px;
        }

        .tables-row {
            gap: 4px;
        }

        .table-compact {
            font-size: 0.7rem;
        }

        .table-compact th,
        .table-compact td {
            padding: 0.2rem 0.3rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Dashboard Header -->
    <div class="dashboard-header animate-fade-in">
        <h1 class="dashboard-title">Enterprise Cloud Operations</h1>
        <p class="dashboard-subtitle">Real-time infrastructure monitoring and management across AWS and Azure</p>
        <div class="dashboard-stats-overview">
            <div class="overview-stat">
                <span class="overview-stat-number" id="total-accounts">0</span>
                <span class="overview-stat-label">Total Accounts</span>
            </div>
            <div class="overview-stat">
                <span class="overview-stat-number" id="total-instances">0</span>
                <span class="overview-stat-label">EC2 Instances</span>
            </div>
            <div class="overview-stat">
                <span class="overview-stat-number" id="running-instances">0</span>
                <span class="overview-stat-label">Running</span>
            </div>
            <div class="overview-stat">
                <span class="overview-stat-number" id="ssm-online">0</span>
                <span class="overview-stat-label">SSM Online</span>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="metrics-grid animate-fade-in">
        <div class="metric-card">
            <div class="metric-header">
                <span class="metric-title">Infrastructure Health</span>
                <div class="metric-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-heartbeat"></i>
                </div>
            </div>
            <div class="metric-value" id="health-score">98%</div>
            <div class="metric-change positive">
                <i class="fas fa-arrow-up"></i>
                <span>+2% from last week</span>
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-header">
                <span class="metric-title">Cost Optimization</span>
                <div class="metric-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
            <div class="metric-value" id="cost-savings">$12.4K</div>
            <div class="metric-change positive">
                <i class="fas fa-arrow-up"></i>
                <span>Saved this month</span>
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-header">
                <span class="metric-title">Security Score</span>
                <div class="metric-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-shield-alt"></i>
                </div>
            </div>
            <div class="metric-value" id="security-score">94%</div>
            <div class="metric-change neutral">
                <i class="fas fa-minus"></i>
                <span>No change</span>
            </div>
        </div>

        <div class="metric-card">
            <div class="metric-header">
                <span class="metric-title">Automation Rate</span>
                <div class="metric-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="fas fa-robot"></i>
                </div>
            </div>
            <div class="metric-value" id="automation-rate">87%</div>
            <div class="metric-change positive">
                <i class="fas fa-arrow-up"></i>
                <span>+5% this quarter</span>
            </div>
        </div>
    </div>

    <!-- Cloud Infrastructure Sections -->
    <div class="cloud-sections animate-fade-in">
        <!-- AWS Section -->
        <div class="cloud-section">
            <div class="section-header aws-header">
                <i class="fab fa-aws cloud-icon"></i>
                <span class="cloud-name">Amazon Web Services</span>
            </div>
            <div class="section-content">
                <div class="data-grid">
                    <!-- AWS Accounts by Business Unit -->
                    <div class="data-widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <div class="widget-icon" style="background: linear-gradient(135deg, #FF9900, #FF6600);">
                                    <i class="fas fa-building"></i>
                                </div>
                                AWS Accounts by Business Unit
                            </div>
                        </div>
                        <div class="bu-chart" id="aws-accounts-chart">
                            <!-- Data will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- AWS EC2 Instances -->
                    <div class="data-widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <div class="widget-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                    <i class="fas fa-server"></i>
                                </div>
                                EC2 Instance Health
                            </div>
                        </div>
                        <div class="bu-chart" id="aws-ec2-chart">
                            <!-- Data will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- AWS Status Overview -->
                <div class="status-overview">
                    <div class="status-indicator healthy">
                        <i class="fas fa-check-circle"></i>
                        <span>All systems operational</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Azure Section -->
        <div class="cloud-section">
            <div class="section-header azure-header">
                <i class="fab fa-microsoft cloud-icon"></i>
                <span class="cloud-name">Microsoft Azure</span>
            </div>
            <div class="section-content">
                <div class="data-grid">
                    <!-- Azure Subscriptions -->
                    <div class="data-widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <div class="widget-icon" style="background: linear-gradient(135deg, #0078D4, #005A9E);">
                                    <i class="fas fa-cloud"></i>
                                </div>
                                Azure Subscriptions
                            </div>
                        </div>
                        <div class="text-center" style="padding: 40px 0;">
                            <i class="fas fa-rocket fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Coming Soon</h5>
                            <p class="text-muted">Azure integration in development</p>
                        </div>
                    </div>

                    <!-- Azure Virtual Machines -->
                    <div class="data-widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <div class="widget-icon" style="background: linear-gradient(135deg, #6366f1, #4f46e5);">
                                    <i class="fas fa-desktop"></i>
                                </div>
                                Virtual Machines
                            </div>
                        </div>
                        <div class="text-center" style="padding: 40px 0;">
                            <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">In Development</h5>
                            <p class="text-muted">VM monitoring capabilities</p>
                        </div>
                    </div>
                </div>

                <!-- Azure Status Overview -->
                <div class="status-overview">
                    <div class="status-indicator info">
                        <i class="fas fa-info-circle"></i>
                        <span>Integration planned for Q2 2024</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions animate-fade-in">
        <a href="{% url 'inventory:ec2_instances' %}" class="action-card">
            <div class="action-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                <i class="fas fa-server"></i>
            </div>
            <div class="action-title">EC2 Instances</div>
            <div class="action-description">Manage and monitor EC2 infrastructure</div>
        </a>

        <a href="{% url 'inventory:ssm_status' %}" class="action-card">
            <div class="action-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                <i class="fas fa-heartbeat"></i>
            </div>
            <div class="action-title">SSM Status</div>
            <div class="action-description">Systems Manager agent monitoring</div>
        </a>

        <a href="{% url 'inventory:accounts' %}" class="action-card">
            <div class="action-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                <i class="fas fa-users"></i>
            </div>
            <div class="action-title">AWS Accounts</div>
            <div class="action-description">Account management and overview</div>
        </a>

        <a href="{% url 'inventory:eks_clusters' %}" class="action-card">
            <div class="action-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                <i class="fas fa-dharmachakra"></i>
            </div>
            <div class="action-title">EKS Clusters</div>
            <div class="action-description">Kubernetes cluster management</div>
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let dashboardData = {
        accounts: [],
        instances: [],
        stats: {}
    };

    function loadDashboardData() {
        showLoading();

        // Load all data concurrently
        Promise.all([
            loadDashboardStats(),
            loadAWSAccounts(),
            loadEC2Instances()
        ]).then(() => {
            updateDashboardOverview();
            updateAWSCharts();
            hideLoading();

            // Add animation classes
            setTimeout(() => {
                $('.animate-fade-in').addClass('animate-fade-in');
            }, 100);
        }).catch(error => {
            console.error('Dashboard loading error:', error);
            hideLoading();
            showAlert('Failed to load dashboard data', 'danger');
        });
    }

    function loadDashboardStats() {
        return $.ajax({
            url: '/api/dashboard-stats/',
            method: 'GET'
        }).done(function(data) {
            dashboardData.stats = data;
        });
    }

    function loadAWSAccounts() {
        return $.ajax({
            url: '/api/accounts/',
            method: 'GET',
            data: { page_size: 1000 }
        }).done(function(data) {
            dashboardData.accounts = data.results || [];
        });
    }

    function loadEC2Instances() {
        return $.ajax({
            url: '/api/ec2-instances/',
            method: 'GET',
            data: { page_size: 1000 }
        }).done(function(data) {
            dashboardData.instances = data.results || [];
        });
    }

    function updateDashboardOverview() {
        const stats = dashboardData.stats;

        // Update overview stats
        $('#total-accounts').text(stats.total_accounts || 0);
        $('#total-instances').text(stats.total_instances || 0);
        $('#running-instances').text(stats.running_instances || 0);
        $('#ssm-online').text(stats.ssm_online || 0);

        // Calculate and update health score
        const healthScore = calculateHealthScore();
        $('#health-score').text(healthScore + '%');

        // Update other metrics (simulated for demo)
        $('#cost-savings').text('$' + (Math.random() * 20 + 5).toFixed(1) + 'K');
        $('#security-score').text((Math.random() * 10 + 90).toFixed(0) + '%');
        $('#automation-rate').text((Math.random() * 15 + 80).toFixed(0) + '%');
    }

    function calculateHealthScore() {
        const stats = dashboardData.stats;
        if (!stats.total_instances || stats.total_instances === 0) return 100;

        const runningPercentage = (stats.running_instances / stats.total_instances) * 100;
        const ssmPercentage = stats.total_instances > 0 ? (stats.ssm_online / stats.total_instances) * 100 : 100;

        return Math.round((runningPercentage * 0.6) + (ssmPercentage * 0.4));
    }

    function updateAWSCharts() {
        updateAWSAccountsChart();
        updateAWSEC2Chart();
    }

    function updateAWSAccountsChart() {
        const accounts = dashboardData.accounts;
        const buGroups = {};

        // Group accounts by business unit
        accounts.forEach(account => {
            const bu = account.business_unit || 'Unknown';
            buGroups[bu] = (buGroups[bu] || 0) + 1;
        });

        const chartContainer = $('#aws-accounts-chart');
        chartContainer.empty();

        // Sort by count descending
        const sortedBUs = Object.entries(buGroups).sort((a, b) => b[1] - a[1]);

        sortedBUs.forEach(([bu, count]) => {
            const percentage = accounts.length > 0 ? (count / accounts.length) * 100 : 0;

            const buItem = $(`
                <div class="bu-item">
                    <div class="bu-name">${bu}</div>
                    <div class="bu-metrics">
                        <div class="bu-metric">
                            <span class="bu-metric-value">${count}</span>
                            <span class="bu-metric-label">Accounts</span>
                        </div>
                    </div>
                </div>
            `);

            chartContainer.append(buItem);
        });

        if (sortedBUs.length === 0) {
            chartContainer.append('<div class="text-center text-muted">No accounts data available</div>');
        }
    }

    function updateAWSEC2Chart() {
        const instances = dashboardData.instances;
        const buStats = {};

        // Group instances by business unit and calculate stats
        instances.forEach(instance => {
            const bu = instance.business_unit || 'Unknown';
            if (!buStats[bu]) {
                buStats[bu] = {
                    total: 0,
                    running: 0,
                    ssmOnline: 0
                };
            }

            buStats[bu].total++;

            if (instance.state === 'running') {
                buStats[bu].running++;
            }

            if (instance.ssm_ping_status === 'Online') {
                buStats[bu].ssmOnline++;
            }
        });

        const chartContainer = $('#aws-ec2-chart');
        chartContainer.empty();

        // Sort by total instances descending
        const sortedBUs = Object.entries(buStats).sort((a, b) => b[1].total - a[1].total);

        sortedBUs.forEach(([bu, stats]) => {
            const healthPercentage = stats.total > 0 ? (stats.running / stats.total) * 100 : 0;
            const ssmPercentage = stats.total > 0 ? (stats.ssmOnline / stats.total) * 100 : 0;

            const buItem = $(`
                <div class="bu-item">
                    <div class="bu-name">${bu}</div>
                    <div class="bu-metrics">
                        <div class="bu-metric">
                            <span class="bu-metric-value">${stats.total}</span>
                            <span class="bu-metric-label">Total</span>
                        </div>
                        <div class="bu-metric">
                            <span class="bu-metric-value">${stats.running}</span>
                            <span class="bu-metric-label">Running</span>
                        </div>
                        <div class="bu-metric">
                            <span class="bu-metric-value">${stats.ssmOnline}</span>
                            <span class="bu-metric-label">SSM</span>
                        </div>
                    </div>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar success" style="width: ${healthPercentage}%"></div>
                </div>
            `);

            chartContainer.append(buItem);
        });

        if (sortedBUs.length === 0) {
            chartContainer.append('<div class="text-center text-muted">No EC2 instances data available</div>');
        }
    }

    // Utility functions
    function showAlert(message, type = 'info') {
        // Create a modern toast notification
        const toast = $(`
            <div class="toast-notification ${type}" style="
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 9999;
                background: white;
                border-radius: 12px;
                padding: 16px 20px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.12);
                border-left: 4px solid var(--accent-color);
                transform: translateX(400px);
                transition: transform 0.3s ease;
                max-width: 350px;
            ">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-info-circle" style="color: var(--accent-color);"></i>
                    <span>${message}</span>
                    <button onclick="$(this).closest('.toast-notification').remove()" style="
                        background: none;
                        border: none;
                        font-size: 1.2rem;
                        cursor: pointer;
                        opacity: 0.7;
                        margin-left: auto;
                    ">&times;</button>
                </div>
            </div>
        `);

        $('body').append(toast);

        // Show with animation
        setTimeout(() => toast.css('transform', 'translateX(0)'), 100);

        // Auto-hide after 4 seconds
        setTimeout(() => {
            toast.css('transform', 'translateX(400px)');
            setTimeout(() => toast.remove(), 300);
        }, 4000);
    }

    // Make functions available globally
    window.showAlert = showAlert;

    // Initialize dashboard
    $(document).ready(function() {
        // Add loading animation
        $('.animate-fade-in').css('opacity', '0');

        // Load dashboard data
        loadDashboardData();

        // Refresh data every 5 minutes
        setInterval(loadDashboardData, 5 * 60 * 1000);

        // Add hover effects to metric cards
        $('.metric-card').hover(
            function() {
                $(this).find('.metric-icon').css('transform', 'scale(1.1)');
            },
            function() {
                $(this).find('.metric-icon').css('transform', 'scale(1)');
            }
        );
    });

    // Make functions available globally
    window.loadData = loadDashboardData;
    window.refreshDashboard = loadDashboardData;
</script>
{% endblock %}
