{% extends "admin/change_list.html" %}
{% load i18n admin_urls static %}

{% block extrahead %}
{{ block.super }}
<style>
    .bulk-upload-btn {
        background: #28a745;
        color: white;
        padding: 8px 16px;
        text-decoration: none;
        border-radius: 4px;
        display: inline-block;
        margin-left: 10px;
        font-size: 13px;
        transition: background 0.3s ease;
    }

    .bulk-upload-btn:hover {
        background: #218838;
        color: white;
        text-decoration: none;
    }

    .bulk-upload-btn i {
        margin-right: 5px;
    }

    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 20px 0;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #007cba;
    }

    .stat-card {
        background: white;
        padding: 15px;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #007cba;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12px;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .quick-stats {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .quick-stat {
        background: white;
        padding: 10px 15px;
        border-radius: 20px;
        border: 1px solid #ddd;
        font-size: 13px;
        color: #333;
    }

    .quick-stat.active {
        background: #d4edda;
        border-color: #28a745;
        color: #155724;
    }

    .quick-stat.inactive {
        background: #f8d7da;
        border-color: #dc3545;
        color: #721c24;
    }

    .enhanced-search {
        background: white;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
        border: 1px solid #ddd;
    }

    .search-row {
        display: flex;
        gap: 10px;
        align-items: center;
        flex-wrap: wrap;
    }

    .search-input {
        flex: 1;
        min-width: 200px;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .search-btn {
        background: #007cba;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }

    .search-btn:hover {
        background: #005a87;
    }

    .filter-tags {
        display: flex;
        gap: 8px;
        margin-top: 10px;
        flex-wrap: wrap;
    }

    .filter-tag {
        background: #e9ecef;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        color: #495057;
    }

    .filter-tag.active {
        background: #007cba;
        color: white;
    }

    .top-lists {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: 15px;
    }

    .top-list {
        background: white;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #ddd;
    }

    .top-list h4 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 14px;
        font-weight: bold;
    }

    .top-item {
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
        border-bottom: 1px solid #f0f0f0;
        font-size: 13px;
    }

    .top-item:last-child {
        border-bottom: none;
    }

    .top-item-name {
        color: #333;
    }

    .top-item-count {
        color: #666;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block object-tools %}
<ul class="object-tools">
    {% block object-tools-items %}
        <li>
            <a href="{% url 'admin:inventory_awsaccount_bulk_upload' %}" class="bulk-upload-btn">
                <i class="fas fa-cloud-upload-alt"></i>
                Bulk Upload CSV
            </a>
        </li>
        {% if has_add_permission %}
            <li>
                <a href="{% url 'admin:inventory_awsaccount_add' %}" class="addlink">
                    {% trans 'Add AWS Account' %}
                </a>
            </li>
        {% endif %}
    {% endblock %}
</ul>
{% endblock %}

{% block content_title %}
<h1>
    <i class="fas fa-cloud"></i> AWS Accounts Manager
    <span style="font-size: 14px; color: #666; font-weight: normal;">
        ({{ cl.result_count }} of {{ total_accounts }} total)
    </span>
</h1>

<!-- Statistics Dashboard -->
<div class="stats-container">
    <div class="stat-card">
        <div class="stat-number">{{ total_accounts }}</div>
        <div class="stat-label">Total Accounts</div>
    </div>
    <div class="stat-card">
        <div class="stat-number" style="color: #28a745;">{{ active_accounts }}</div>
        <div class="stat-label">Active Accounts</div>
    </div>
    <div class="stat-card">
        <div class="stat-number" style="color: #dc3545;">{{ inactive_accounts }}</div>
        <div class="stat-label">Inactive Accounts</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ bu_stats|length }}</div>
        <div class="stat-label">Business Units</div>
    </div>
</div>

<!-- Quick Stats -->
<div class="quick-stats">
    <div class="quick-stat active">
        <i class="fas fa-check-circle"></i> {{ active_accounts }} Active
    </div>
    <div class="quick-stat inactive">
        <i class="fas fa-times-circle"></i> {{ inactive_accounts }} Inactive
    </div>
    {% for bu in bu_stats|slice:":3" %}
    <div class="quick-stat">
        <i class="fas fa-building"></i> {{ bu.business_unit }}: {{ bu.count }}
    </div>
    {% endfor %}
</div>

<!-- Enhanced Search -->
<div class="enhanced-search">
    <form method="get" id="enhanced-search-form">
        <div class="search-row">
            <input type="text"
                   name="q"
                   value="{{ cl.query }}"
                   placeholder="Search by account name, ID, business unit, or region..."
                   class="search-input"
                   id="searchbox">
            <button type="submit" class="search-btn">
                <i class="fas fa-search"></i> Search
            </button>
            {% if cl.query %}
            <a href="?" class="search-btn" style="background: #6c757d; text-decoration: none;">
                <i class="fas fa-times"></i> Clear
            </a>
            {% endif %}
        </div>

        <!-- Filter Tags -->
        <div class="filter-tags">
            {% for filter_spec in cl.filter_specs %}
                {% for choice in filter_spec.choices %}
                    {% if choice.selected %}
                    <span class="filter-tag active">
                        {{ filter_spec.title }}: {{ choice.display }}
                        <a href="{{ choice.query_string }}" style="color: white; margin-left: 5px;">×</a>
                    </span>
                    {% endif %}
                {% endfor %}
            {% endfor %}
        </div>
    </form>
</div>

<!-- Top Business Units and Regions -->
{% if bu_stats or region_stats %}
<div class="top-lists">
    <div class="top-list">
        <h4><i class="fas fa-building"></i> Top Business Units</h4>
        {% for bu in bu_stats %}
        <div class="top-item">
            <span class="top-item-name">{{ bu.business_unit }}</span>
            <span class="top-item-count">{{ bu.count }}</span>
        </div>
        {% endfor %}
    </div>

    <div class="top-list">
        <h4><i class="fas fa-globe"></i> Top Regions</h4>
        {% for region in region_stats %}
        <div class="top-item">
            <span class="top-item-name">{{ region.region }}</span>
            <span class="top-item-count">{{ region.count }}</span>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}
{% endblock %}

{% block result_list %}
{{ block.super }}

{% if cl.result_count > 0 %}
<div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007cba;">
    <h3 style="margin-top: 0; color: #007cba;">
        <i class="fas fa-tools"></i> Bulk Operations & Quick Actions
    </h3>

    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-bottom: 15px;">
        <div>
            <h4 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">
                <i class="fas fa-upload"></i> Import/Export
            </h4>
            <p style="margin: 0 0 5px 0; font-size: 13px;">
                <strong>Upload CSV:</strong> Use "Bulk Upload CSV" button to import accounts
            </p>
            <p style="margin: 0; font-size: 13px;">
                <strong>Export CSV:</strong> Select accounts and use "Export selected accounts" action
            </p>
        </div>

        <div>
            <h4 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">
                <i class="fas fa-cogs"></i> Account Management
            </h4>
            <p style="margin: 0 0 5px 0; font-size: 13px;">
                <strong>Activate:</strong> Select accounts and use "Activate selected accounts" action
            </p>
            <p style="margin: 0; font-size: 13px;">
                <strong>Deactivate:</strong> Select accounts and use "Deactivate selected accounts" action
            </p>
        </div>

        <div>
            <h4 style="margin: 0 0 8px 0; color: #333; font-size: 14px;">
                <i class="fas fa-search"></i> Search & Filter
            </h4>
            <p style="margin: 0 0 5px 0; font-size: 13px;">
                <strong>Search:</strong> Use search box above for account name, ID, BU, or region
            </p>
            <p style="margin: 0; font-size: 13px;">
                <strong>Filter:</strong> Use sidebar filters for Business Unit, Region, and Status
            </p>
        </div>
    </div>

    <div style="background: #e9ecef; padding: 10px; border-radius: 4px; font-size: 12px; color: #666;">
        <strong>CSV Format:</strong> account_id, account_name, region, bu |
        <strong>Showing:</strong> {{ cl.result_count }} of {{ total_accounts }} accounts |
        <strong>Page:</strong> {{ cl.paginator.page_range|length }} pages
    </div>
</div>

<!-- Search Results Info -->
{% if cl.query %}
<div style="margin: 15px 0; padding: 10px; background: #d1ecf1; border-radius: 4px; border-left: 4px solid #17a2b8;">
    <strong><i class="fas fa-search"></i> Search Results:</strong>
    Found {{ cl.result_count }} accounts matching "{{ cl.query }}"
    {% if cl.result_count != total_accounts %}
    <a href="?" style="margin-left: 10px; color: #17a2b8;">Show all {{ total_accounts }} accounts</a>
    {% endif %}
</div>
{% endif %}

<!-- Filter Results Info -->
{% if cl.has_filters %}
<div style="margin: 15px 0; padding: 10px; background: #fff3cd; border-radius: 4px; border-left: 4px solid #ffc107;">
    <strong><i class="fas fa-filter"></i> Filtered Results:</strong>
    Showing {{ cl.result_count }} accounts with active filters
    <a href="?" style="margin-left: 10px; color: #856404;">Clear all filters</a>
</div>
{% endif %}
{% endif %}

<script>
// Enhanced search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchBox = document.getElementById('searchbox');
    if (searchBox) {
        // Auto-focus search box
        searchBox.focus();

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+F or Cmd+F to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                searchBox.focus();
                searchBox.select();
            }

            // Escape to clear search
            if (e.key === 'Escape' && document.activeElement === searchBox) {
                if (searchBox.value) {
                    searchBox.value = '';
                } else {
                    searchBox.blur();
                }
            }
        });

        // Search suggestions (simple implementation)
        searchBox.addEventListener('input', function() {
            // Could add autocomplete functionality here
        });
    }
});
</script>
{% endblock %}
