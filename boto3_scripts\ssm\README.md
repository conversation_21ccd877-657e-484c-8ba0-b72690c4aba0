# SSM Scripts

This directory contains scripts for AWS Systems Manager (SSM) monitoring and management.

## Scripts

### `ssm_status_check.py`
SSM agent status monitoring and compliance checking script.

**Features:**
- Monitors SSM agent status across all EC2 instances
- Checks agent version and latest version compliance
- Retrieves last ping datetime and platform information
- Correlates SSM data with EC2 instance information
- Identifies instances without SSM agent or with connectivity issues
- Generates Excel reports with comprehensive SSM status data

**Output Fields:**
- Business Unit, Account Name, Account ID, Region
- Instance ID, Instance Name, Instance State
- SSM Agent Status (Online/Offline/Inactive)
- Agent Version, Is Latest Version
- Last Ping DateTime
- Platform Name, Platform Type
- OS Information from SSM
- Instance specifications and network details

**Usage:**
```bash
python ssm_status_check.py
```

**Requirements:**
- AWS profiles configured for target accounts
- IAM permissions for SSM and EC2 services
- Python dependencies: boto3, pandas, openpyxl

**Configuration:**
- Reads account information from roles configuration file
- Uses AWS profiles in format: `support-{account_id}`
- Supports multi-threading for faster processing

**Use Cases:**
- SSM agent compliance monitoring
- Patch management readiness assessment
- Instance connectivity troubleshooting
- Security compliance reporting
- Infrastructure health monitoring
