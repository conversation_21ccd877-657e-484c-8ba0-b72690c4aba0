import boto3
import time
import pandas as pd
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side
import csv
import logging
from datetime import datetime, timezone
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed
from botocore.config import Config
from botocore.exceptions import BotoCoreError, ClientError
import os
import certifi

os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()
warnings.filterwarnings('ignore')
now_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
logging.basicConfig(filename=f'aws_ec2_ssm_status_check_{now_time}.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
start_time_script = time.time()
start_str_script = time.strftime("%H:%M:%S", time.gmtime(start_time_script))
logging.info(f"script Started at {start_str_script}")

def get_instance_ids(ec2_client):
    instance_ids = []
    paginator = ec2_client.get_paginator('describe_instances')
    try:
        for page in paginator.paginate(
            Filters=[{'Name': 'instance-state-name', 'Values': ['running']}]
        ):
            for reservation in page['Reservations']:
                for instance in reservation['Instances']:
                    instance_id = instance['InstanceId']
                    instance_ids.append(instance_id)
    
        return instance_ids
    except (BotoCoreError, ClientError) as e:
        logging.error(f"Error getting instance ids: {str(e)}")
        return []


def check_instance_status(ec2_client, instance_ids):
    try:
        statuses = ec2_client.describe_instance_status(InstanceIds=instance_ids)
        if all(instance['InstanceStatus']['Status'] == 'ok' and instance['SystemStatus']['Status'] == 'ok' for instance in statuses['InstanceStatuses']):
            return statuses
        else:
            return statuses
    except (BotoCoreError, ClientError) as e:
        logging.error(f"Error checking instance status: {str(e)}")
        return {}

def save_to_csv(access_denied, data, errordata, output_file):
    # Define headers
    headers_main = [
        'Business Unit', 'Account ID', 'Account Name', 'Region', 'CTO Cloud Ops Managed',
        'Instance ID', 'State', 'Environment', 'Private Ip Address', 'SSM Status', 
        'Agent Version', 'Is Latest Version', 'Last Ping', 'Instance Platform', 'OS Info'
    ]
    headers_errors = ['BU', 'Account ID', 'Account Name', 'Region', 'Instance ID', 'Reason']
    headers_access = ['BU', 'Account ID', 'ERROR', 'Reason']

    # Create dataframes
    df_main = pd.DataFrame(data, columns=headers_main)
    df_errordata = pd.DataFrame(errordata, columns=headers_errors)
    df_access = pd.DataFrame(access_denied, columns=headers_access)

    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # Write dataframes to Excel sheets
            df_main.to_excel(writer, index=False, sheet_name='Ec2_Instance_SSM_Status')
            df_errordata.to_excel(writer, index=False, sheet_name='Ec2_Instance_Error')
            df_access.to_excel(writer, index=False, sheet_name='ACCESS_DENIED')

            # Access the workbook and specific sheets
            wb = writer.book
            ws_main = wb['Ec2_Instance_SSM_Status']
            ws_error = wb['Ec2_Instance_Error']
            ws_access = wb['ACCESS_DENIED']

            # Formatting for headers in the main sheet
            header_fill = PatternFill(start_color='017054', end_color='017054', fill_type='solid')
            header_font = Font(bold=True, color='FFFFFF')  # White font for contrast
            for cell in ws_main[1]:
                cell.fill = header_fill
                cell.font = header_font

            # Apply borders to all cells in the main sheet
            border_style = Border(
                left=Side(style='thin'), 
                right=Side(style='thin'), 
                top=Side(style='thin'), 
                bottom=Side(style='thin')
            )
            for row in ws_main.iter_rows(min_row=2, max_row=ws_main.max_row, min_col=1, max_col=ws_main.max_column):
                for cell in row:
                    cell.border = border_style

        logging.info(f"Excel file successfully saved: {output_file}")
    except Exception as e:
        logging.error(f"Failed to save Excel file: {e}")

def read_roles():
    roles = []
    with open('ge_vn_accounts.txt', 'r') as file:
        for line in file.readlines():
            account_id, region, bu = line.strip().split(',')
            roles.append((account_id, region, bu))
    return roles



def process_account(role_info):
    print(role_info)
    account_id, role_region, bu = role_info
    access_denied = []
    data = []
    # data2 = []
    errordata = []
    start_time_account = None
    try:
        print(f"switched to account {account_id, role_region, bu}")
        profile_name = f"support-{account_id}"
        print(profile_name)
        session = boto3.session.Session(profile_name=profile_name, region_name=role_region)
        print(session)
        

        iam_client = session.client('iam', region_name=role_region,verify=False)
        
        try:
            account_aliases = iam_client.list_account_aliases().get('AccountAliases', [])
            account_name = account_aliases[0] if account_aliases else 'Unknown'
        except KeyError:
            account_name = 'Unknown'
        
        account_id = account_id
        start_time_account = time.time()
        start_datetime_acc = datetime.now().strftime("%H:%M:%S")
        logging.info(f"Fetching report from: {account_name} :: {account_id} :: {role_region} started at: {start_datetime_acc}")
        
        
        ec2_client = session.client('ec2', verify=False)
        ssm = session.client('ssm', verify=False)
        
        instance_ids = get_instance_ids(ec2_client)
        if instance_ids:
            for instance_id in instance_ids:
                response = ec2_client.describe_instances(InstanceIds=[instance_id])
                if response['Reservations'] and response['Reservations'][0]['Instances']:
                    try:
                        instance = response['Reservations'][0]['Instances'][0]
                        instance_type = instance['PlatformDetails']
                        state = instance['State']['Name']
                        private_ip_address = instance.get('PrivateIpAddress', 'N/A')
                        tags = instance.get('Tags', [])
                        tag_env = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'env'), '')
                        CTOCloudOpsManaged = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'ctocloudopsmanaged'), 'NotAssigned')

                        region_report = role_region
                        try:
                            ssm_response = ssm.describe_instance_information(
                                InstanceInformationFilterList=[
                                    {'key': 'InstanceIds', 'valueSet': [instance_id]}
                                ]
                            )
                            if ssm_response['InstanceInformationList']:
                                instance_info = ssm_response['InstanceInformationList'][0]
                                
                                # if instance_info['PingStatus'] == 'Online':
                                agent_status = instance_info['PingStatus']
                                agent_version = instance_info['AgentVersion']
                                last_ping_1 = instance_info['LastPingDateTime']
                                last_ping_2 = last_ping_1.replace(tzinfo=None)
                                formatted_last_ping = last_ping_2.strftime("%Y-%m-%d %H:%M:%S")
                                last_ping = formatted_last_ping

                                print(f"{bu} : {account_id} : {account_name}: {instance_id} : {last_ping}")
                                is_latest_ver = instance_info['IsLatestVersion']
                                Platform_name = instance_info.get('PlatformName', 'Not available')
                                Platform = instance_info['PlatformType']
                                logging.info(f"{bu} : {account_id} : {account_name} : Agent is :{agent_status} : {instance_id}" )
                                data.append([bu, account_id, account_name, region_report, CTOCloudOpsManaged, instance_id, state, tag_env, private_ip_address, f'Agent {agent_status}', agent_version, is_latest_ver, last_ping, Platform, Platform_name])
                                # else:
                                #     print(instance_id)
                                # logging.warning(f"{bu} : {account_id} : {account_name} Agent is :{agent_status} : {instance_id}")
                                #     data.append([bu, account_id, account_name, region_report, instance_id, state, tag_Name, tag_env, instance_type, private_ip_address, 'Agent Offline', CTOCloudOpsManaged ])
                            else:
                                logging.warning(f"{bu} : {account_id} : {account_name} : No SSM information found  : {instance_id}")
                                data.append([bu, account_id, account_name, region_report, CTOCloudOpsManaged, instance_id, state, tag_env, private_ip_address, 'SSM Not configured / SSM defective', 'N/A', 'N/A', 'N/A', instance_type, 'N/A'])

                        except Exception as e:
                            logging.error(f"Error checking SSM agent for instance {instance_id}: {str(e)}")
                            errordata.append([bu, account_id, account_name, region_report, instance_id, str(e)]) 
                    except Exception as e:
                        errordata.append([bu, account_id, account_name, region_report, instance_id, str(e)])
    except Exception as e:
        logging.error(f"{str(e)}")
        access_denied.append([bu, account_id, 'ACCESS_DENIED', str(e)])

    if start_time_account:

        elapsed_time_account = time.time() - start_time_account
        elapsed_str_account = time.strftime("%H:%M:%S", time.gmtime(elapsed_time_account))
        logging.info(f"Fetching completed for: {account_name} :: {account_id} :: {role_region} :: {elapsed_str_account}")
       

    return access_denied, data, errordata

if __name__ == "__main__":
    roles = read_roles()
    now = datetime.now().strftime("%Y-%m-%d_%H-%M")
    output_file = f'AWS_EC2_SSM_STATUS_CHECK_{now}.xlsx'

    access_denied = []
    data = []
    # data2 = []
    errordata = []

    with ThreadPoolExecutor(max_workers=55) as executor:
        futures = [executor.submit(process_account, role) for role in roles]

        for future in as_completed(futures):
            try:
                result = future.result()
                access_denied.extend(result[0])
                data.extend(result[1])
                # data2.extend(result[2])
                errordata.extend(result[2])
            except Exception as e:
                logging.error(f"Error processing account: {str(e)}")

    save_to_csv(access_denied, data, errordata, output_file)
    logging.info(f'Data saved to {output_file}')
    elapsed_time_script = time.time() - start_time_script
    elapsed_str_script = time.strftime("%H:%M:%S", time.gmtime(elapsed_time_script))
    logging.info(f"script Started at {start_str_script}")
    logging.info(f"script completed in {elapsed_str_script}")
