# AWS Inventory Management System

A comprehensive Django web application for managing AWS infrastructure inventory with REST API and modern frontend interface.

## Features

### 🚀 Core Functionality
- **EC2 Instance Management**: Complete inventory of EC2 instances across multiple AWS accounts
- **SSM Agent Monitoring**: Real-time monitoring of Systems Manager agent status
- **Multi-Account Support**: Manage multiple AWS accounts and regions
- **REST API**: Full REST API for programmatic access
- **Modern Web Interface**: Responsive HTML/CSS/JavaScript frontend with AJAX

### 📊 Dashboard & Analytics
- Real-time statistics and metrics
- Instance state distribution charts
- SSM agent status monitoring
- Account-wise inventory breakdown

### 🔍 Advanced Features
- **Filtering & Search**: Advanced filtering by account, state, environment, etc.
- **Export Functionality**: Export data to Excel format
- **Bulk Operations**: Refresh inventory for all accounts
- **Real-time Updates**: AJAX-powered dynamic updates

## Architecture

### Backend
- **Framework**: Django 4.2.7 with Django REST Framework
- **Database**: SQLite (easily configurable for PostgreSQL/MySQL)
- **AWS Integration**: Boto3 for AWS API interactions
- **Authentication**: Django admin authentication

### Frontend
- **UI Framework**: Bootstrap 5.1.3
- **JavaScript**: jQuery with AJAX for dynamic interactions
- **Charts**: Chart.js for data visualization
- **Icons**: Font Awesome 6.0
- **Tables**: DataTables for advanced table functionality

## Installation & Setup

### Prerequisites
- Python 3.8+
- pip package manager

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Database Setup
```bash
python manage.py makemigrations
python manage.py migrate
```

### 3. Create Superuser
```bash
python manage.py createsuperuser
```

### 4. Load Sample Data (Optional)
```bash
python manage.py load_sample_data
```

### 5. Run the Application
```bash
python manage.py runserver
# or
python run_server.py
```

The application will be available at:
- **Main Application**: http://127.0.0.1:8000
- **Admin Interface**: http://127.0.0.1:8000/admin
- **API Documentation**: http://127.0.0.1:8000/api/

## API Endpoints

### Core Resources
- `GET /api/accounts/` - List AWS accounts
- `POST /api/accounts/` - Create new account
- `GET /api/accounts/{id}/` - Get account details
- `POST /api/accounts/{id}/refresh_inventory/` - Refresh account inventory

- `GET /api/ec2-instances/` - List EC2 instances
- `GET /api/ec2-instances/export_excel/` - Export instances to Excel
- `GET /api/ec2-instances/{id}/` - Get instance details

- `GET /api/ssm-status/` - List SSM status records
- `GET /api/ssm-status/{id}/` - Get SSM status details

### Utility Endpoints
- `GET /api/dashboard-stats/` - Get dashboard statistics
- `POST /api/refresh-all/` - Refresh all account inventories

## Data Models

### AWSAccount
- Account ID, Name, Business Unit, Region
- Active status and metadata
- Related EC2 instances and refresh logs

### EC2Instance
- Complete instance information (ID, type, state, IPs, etc.)
- Resource specifications (vCPU, memory, storage)
- AWS tags (environment, UAI, patch info, etc.)
- Relationship to AWS account

### SSMStatus
- SSM agent status and version information
- Last ping datetime and platform details
- Configuration status

### InventoryRefreshLog
- Audit trail for inventory refresh operations
- Success/failure tracking and error details

## Frontend Pages

### 1. Dashboard (`/`)
- Overview statistics and charts
- Recent instances table
- Quick access to all features

### 2. EC2 Instances (`/ec2-instances/`)
- Comprehensive instance listing
- Advanced filtering and search
- Export to Excel functionality
- Detailed instance modal views

### 3. SSM Status (`/ssm-status/`)
- SSM agent status monitoring
- Status distribution summary
- Filtering by status and account

### 4. Account Management (`/accounts/`)
- AWS account CRUD operations
- Individual account inventory refresh
- Account status management

## AWS Integration

The application integrates with AWS services using boto3 and supports:

### Authentication
- AWS profile-based authentication
- Support for cross-account roles
- Configurable regions per account

### Supported Services
- **EC2**: Instance inventory and specifications
- **SSM**: Agent status and platform information
- **IAM**: Account alias resolution

### Data Collection
Based on the original boto3 scripts, the application collects:
- Instance metadata and specifications
- Storage configuration (root and data volumes)
- Network configuration (IPs, DNS names)
- AWS tags and custom metadata
- SSM agent status and version information

## Configuration

### Settings
Key configuration options in `aws_inventory/settings.py`:
- Database configuration
- AWS service settings
- REST framework pagination
- CORS settings for API access

### Environment Variables
- `DJANGO_SETTINGS_MODULE`: Django settings module
- AWS credentials via standard AWS credential chain

## Testing

Run the comprehensive test suite:
```bash
python test_app.py
```

This tests:
- Database models and relationships
- API endpoints functionality
- Frontend view rendering
- Data integrity

## Security Considerations

- Django admin authentication required
- CSRF protection enabled
- SQL injection protection via Django ORM
- Input validation and sanitization
- Secure AWS credential handling

## Deployment

### Development
```bash
python manage.py runserver
```

### Production
- Configure proper database (PostgreSQL recommended)
- Set `DEBUG = False` in settings
- Configure static file serving
- Use proper WSGI server (Gunicorn, uWSGI)
- Set up reverse proxy (Nginx)

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request

## License

This project is based on existing boto3 AWS inventory scripts and provides a modern web interface for AWS infrastructure management.

## Support

For issues and questions:
1. Check the test suite output
2. Review Django logs
3. Verify AWS credentials and permissions
4. Check network connectivity to AWS services
