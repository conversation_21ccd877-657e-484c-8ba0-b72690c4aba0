{% extends 'inventory/base.html' %}

{% block title %}SSM Status - Cloud Operations Central{% endblock %}
{% block page_title %}SSM Agent Status{% endblock %}

{% block page_actions %}
<div class="btn-group me-2">
    <button type="button" class="btn btn-outline-secondary" onclick="loadSSMStatus()">
        <i class="fas fa-sync-alt me-1"></i>
        Refresh
    </button>
</div>
<button type="button" class="btn btn-success" onclick="refreshAllInventory()">
    <i class="fas fa-sync-alt me-1"></i>
    Refresh All
</button>
{% endblock %}

{% block content %}
<!-- SSM Status Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-success">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-3x text-success mb-2"></i>
                <h4 id="online-count">-</h4>
                <p class="card-text">Online</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-2"></i>
                <h4 id="connection-lost-count">-</h4>
                <p class="card-text">Connection Lost</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-danger">
            <div class="card-body text-center">
                <i class="fas fa-times-circle fa-3x text-danger mb-2"></i>
                <h4 id="inactive-count">-</h4>
                <p class="card-text">Inactive</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-secondary">
            <div class="card-body text-center">
                <i class="fas fa-question-circle fa-3x text-secondary mb-2"></i>
                <h4 id="unknown-count">-</h4>
                <p class="card-text">Unknown/No SSM</p>
            </div>
        </div>
    </div>
</div>

<!-- Compact Filters -->
<div class="filter-section compact-form">
    <div class="row g-2">
        <div class="col-md-2">
            <select class="form-select form-select-sm" id="filter-bu">
                <option value="">All Business Units</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select form-select-sm" id="filter-account">
                <option value="">All Accounts</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select form-select-sm" id="filter-status">
                <option value="">All Status</option>
                <option value="Online">Online</option>
                <option value="Connection Lost">Connection Lost</option>
                <option value="Inactive">Inactive</option>
            </select>
        </div>
        <div class="col-md-3">
            <input type="text" class="form-control form-control-sm" id="filter-search" placeholder="Search instances...">
        </div>
        <div class="col-md-1">
            <button type="button" class="btn btn-primary btn-sm w-100" onclick="applyFilters()" title="Apply Filters">
                <i class="fas fa-search"></i>
            </button>
        </div>
        <div class="col-md-1">
            <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="clearFilters()" title="Clear Filters">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<!-- Compact SSM Status Table -->
<div class="card compact-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span class="fw-bold">SSM Agent Status Details</span>
        <span class="badge bg-primary" id="status-count">0 instances</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-compact table-striped table-hover mb-0" id="ssm-table">
                <thead>
                    <tr>
                        <th style="width: 140px;">Instance ID</th>
                        <th style="width: 120px;">Name</th>
                        <th style="width: 100px;">Account</th>
                        <th style="width: 70px;">Env</th>
                        <th style="width: 80px;">Status</th>
                        <th style="width: 100px;">Agent Ver</th>
                        <th style="width: 70px;">Latest</th>
                        <th style="width: 120px;">Last Ping</th>
                        <th style="width: 80px;">Platform</th>
                        <th style="width: 120px;">Checked</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>

        <!-- Compact Pagination -->
        <div class="p-2 border-top bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted" id="pagination-info">Showing 0 of 0 instances</small>
                <nav aria-label="SSM status pagination">
                    <ul class="pagination pagination-sm mb-0" id="pagination">
                        <!-- Pagination will be generated dynamically -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let currentFilters = {};

    function loadSSMStatus(page = 1) {
        showLoading();
        
        let url = `/api/ssm-status/?page=${page}`;
        
        // Add filters to URL
        Object.keys(currentFilters).forEach(key => {
            if (currentFilters[key]) {
                url += `&${key}=${encodeURIComponent(currentFilters[key])}`;
            }
        });
        
        $.ajax({
            url: url,
            method: 'GET',
            success: function(data) {
                updateSSMTable(data.results);
                updatePagination(data);
                updateStatusCount(data.count);
                hideLoading();
            },
            error: function(xhr) {
                hideLoading();
                showAlert('Failed to load SSM status data', 'danger');
            }
        });
    }

    function loadSSMSummary() {
        // Load summary statistics
        $.ajax({
            url: '/api/ssm-status/',
            method: 'GET',
            data: { page_size: 1000 }, // Get all for counting
            success: function(data) {
                const statusCounts = {
                    'Online': 0,
                    'Connection Lost': 0,
                    'Inactive': 0,
                    'Unknown': 0
                };
                
                data.results.forEach(function(item) {
                    const status = item.ping_status || 'Unknown';
                    if (statusCounts.hasOwnProperty(status)) {
                        statusCounts[status]++;
                    } else {
                        statusCounts['Unknown']++;
                    }
                });
                
                $('#online-count').text(statusCounts['Online']);
                $('#connection-lost-count').text(statusCounts['Connection Lost']);
                $('#inactive-count').text(statusCounts['Inactive']);
                $('#unknown-count').text(statusCounts['Unknown']);
            },
            error: function(xhr) {
                showAlert('Failed to load SSM summary', 'warning');
            }
        });
    }

    function updateSSMTable(ssmStatuses) {
        const tbody = $('#ssm-table tbody');
        tbody.empty();
        
        if (ssmStatuses && ssmStatuses.length > 0) {
            ssmStatuses.forEach(function(status) {
                const instance = status.instance || {};
                const row = `
                    <tr>
                        <td><code>${instance.instance_id || '-'}</code></td>
                        <td>${instance.name || '-'}</td>
                        <td>${instance.account_name || '-'}</td>
                        <td>${instance.env_tag || '-'}</td>
                        <td><span class="badge bg-${getSSMColor(status.ping_status)}">${status.ping_status || 'Unknown'}</span></td>
                        <td>${status.agent_version || '-'}</td>
                        <td>
                            ${status.is_latest_version !== null ? 
                                (status.is_latest_version ? 
                                    '<span class="badge bg-success">Yes</span>' : 
                                    '<span class="badge bg-warning">No</span>') : 
                                '-'}
                        </td>
                        <td>${formatDate(status.last_ping_datetime)}</td>
                        <td>${status.platform_name || '-'}</td>
                        <td>${formatDate(status.last_checked)}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        } else {
            tbody.append('<tr><td colspan="10" class="text-center">No SSM status data found</td></tr>');
        }
    }

    function updatePagination(data) {
        const pagination = $('#pagination');
        pagination.empty();
        
        if (data.count > 50) { // Assuming page size is 50
            const totalPages = Math.ceil(data.count / 50);
            
            // Previous button
            if (data.previous) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadSSMStatus(${currentPage - 1})">Previous</a>
                    </li>
                `);
            }
            
            // Page numbers (show max 5 pages around current)
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadSSMStatus(${i})">${i}</a>
                    </li>
                `);
            }
            
            // Next button
            if (data.next) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="loadSSMStatus(${currentPage + 1})">Next</a>
                    </li>
                `);
            }
        }
    }

    function updateStatusCount(count) {
        $('#status-count').text(`${count} instances`);
    }

    // Store all accounts data for cascading filters
    let allAccountsData = [];

    function loadFilterOptions() {
        // Load business units and accounts for filter
        $.ajax({
            url: '/api/accounts/',
            method: 'GET',
            data: { page_size: 1000 }, // Get all accounts
            success: function(data) {
                allAccountsData = data.results; // Store for cascading

                const buSelect = $('#filter-bu');

                // Extract unique business units
                const businessUnits = [...new Set(data.results.map(account => account.business_unit))].sort();
                businessUnits.forEach(function(bu) {
                    buSelect.append(`<option value="${bu}">${bu}</option>`);
                });

                // Initially populate all accounts
                updateAccountDropdown();

                // Set up cascading filter event
                buSelect.on('change', function() {
                    updateAccountDropdown();
                    // Auto-apply filters when BU changes
                    applyFilters();
                });
            }
        });
    }

    function updateAccountDropdown() {
        const selectedBU = $('#filter-bu').val();
        const accountSelect = $('#filter-account');

        // Clear current options except the first one
        accountSelect.find('option:not(:first)').remove();

        // Filter accounts based on selected BU
        let accountsToShow = allAccountsData;
        if (selectedBU) {
            accountsToShow = allAccountsData.filter(account => account.business_unit === selectedBU);
        }

        // Update the placeholder text based on selection
        const firstOption = accountSelect.find('option:first');
        if (selectedBU) {
            firstOption.text(`All ${selectedBU} Accounts`);
        } else {
            firstOption.text('All Accounts');
        }

        // Populate filtered accounts
        accountsToShow.forEach(function(account) {
            const displayName = selectedBU ?
                account.account_name : // Show just name if BU is selected
                `${account.account_name} (${account.business_unit})`; // Show with BU if all BUs

            accountSelect.append(`<option value="${account.account_id}">${displayName}</option>`);
        });

        // Reset account selection when BU changes
        accountSelect.val('');

        // Show visual feedback
        if (selectedBU) {
            accountSelect.addClass('border-primary');
            setTimeout(() => accountSelect.removeClass('border-primary'), 1000);
        }
    }

    function applyFilters() {
        currentFilters = {
            business_unit: $('#filter-bu').val(),
            account_id: $('#filter-account').val(),
            ping_status: $('#filter-status').val(),
            search: $('#filter-search').val()
        };
        currentPage = 1;
        loadSSMStatus(1);
    }

    function clearFilters() {
        $('#filter-bu').val('');
        $('#filter-account').val('');
        $('#filter-status').val('');
        $('#filter-search').val('');
        currentFilters = {};
        currentPage = 1;
        loadSSMStatus(1);
        loadSSMSummary();
    }

    function getSSMColor(status) {
        switch(status) {
            case 'Online': return 'success';
            case 'Connection Lost': return 'warning';
            case 'Inactive': return 'danger';
            default: return 'secondary';
        }
    }

    // Load data when page loads
    $(document).ready(function() {
        loadFilterOptions();
        loadSSMStatus();
        loadSSMSummary();
    });

    // Make loadData available globally for base template
    window.loadData = function() {
        loadSSMStatus(currentPage);
        loadSSMSummary();
    };
</script>
{% endblock %}
