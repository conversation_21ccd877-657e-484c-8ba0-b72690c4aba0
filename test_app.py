#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from inventory.models import AWSAccount, EC2Instance, SSMStatus
from django.test import Client
from django.urls import reverse
import json

def test_models():
    print("Testing models...")
    
    # Test AWSAccount
    accounts = AWSAccount.objects.all()
    print(f"Total AWS Accounts: {accounts.count()}")
    
    for account in accounts[:2]:  # Show first 2 accounts
        print(f"  - {account.account_name} ({account.account_id}) - {account.business_unit}")
    
    # Test EC2Instance
    instances = EC2Instance.objects.all()
    print(f"Total EC2 Instances: {instances.count()}")
    
    for instance in instances[:3]:  # Show first 3 instances
        print(f"  - {instance.instance_id} ({instance.name}) - {instance.state}")
    
    # Test SSMStatus
    ssm_statuses = SSMStatus.objects.all()
    print(f"Total SSM Status Records: {ssm_statuses.count()}")
    
    online_count = SSMStatus.objects.filter(ping_status='Online').count()
    print(f"  - Online: {online_count}")
    
    print("Models test completed successfully!\n")

def test_api_endpoints():
    print("Testing API endpoints...")
    
    client = Client()
    
    # Test dashboard stats
    try:
        response = client.get('/api/dashboard-stats/')
        if response.status_code == 200:
            data = response.json()
            print(f"Dashboard stats: {data}")
        else:
            print(f"Dashboard stats failed: {response.status_code}")
    except Exception as e:
        print(f"Dashboard stats error: {e}")
    
    # Test accounts API
    try:
        response = client.get('/api/accounts/')
        if response.status_code == 200:
            data = response.json()
            print(f"Accounts API: Found {len(data.get('results', []))} accounts")
        else:
            print(f"Accounts API failed: {response.status_code}")
    except Exception as e:
        print(f"Accounts API error: {e}")
    
    # Test EC2 instances API
    try:
        response = client.get('/api/ec2-instances/')
        if response.status_code == 200:
            data = response.json()
            print(f"EC2 Instances API: Found {len(data.get('results', []))} instances")
        else:
            print(f"EC2 Instances API failed: {response.status_code}")
    except Exception as e:
        print(f"EC2 Instances API error: {e}")
    
    # Test SSM status API
    try:
        response = client.get('/api/ssm-status/')
        if response.status_code == 200:
            data = response.json()
            print(f"SSM Status API: Found {len(data.get('results', []))} status records")
        else:
            print(f"SSM Status API failed: {response.status_code}")
    except Exception as e:
        print(f"SSM Status API error: {e}")
    
    print("API endpoints test completed!\n")

def test_frontend_views():
    print("Testing frontend views...")
    
    client = Client()
    
    views_to_test = [
        ('/', 'Dashboard'),
        ('/ec2-instances/', 'EC2 Instances'),
        ('/ssm-status/', 'SSM Status'),
        ('/accounts/', 'Accounts'),
    ]
    
    for url, name in views_to_test:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"  ✓ {name} view: OK")
            else:
                print(f"  ✗ {name} view: Failed ({response.status_code})")
        except Exception as e:
            print(f"  ✗ {name} view: Error - {e}")
    
    print("Frontend views test completed!\n")

if __name__ == '__main__':
    print("=== AWS Inventory Application Test ===\n")
    
    try:
        test_models()
        test_api_endpoints()
        test_frontend_views()
        
        print("=== All Tests Completed Successfully! ===")
        print("\nThe Django AWS Inventory application is working correctly!")
        print("You can now start the server with: python manage.py runserver")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
