#!/usr/bin/env python
"""
Fix user migration issues and set up the user management system
"""
import os
import sys
import sqlite3
import shutil
from pathlib import Path

def print_header(title):
    print("\n" + "="*70)
    print(f" {title}")
    print("="*70)

def print_section(title):
    print(f"\n--- {title} ---")

def backup_database():
    """Backup existing database"""
    print_section("Backing up existing database")
    
    if os.path.exists('db.sqlite3'):
        backup_name = f'db_backup_{int(os.path.getmtime("db.sqlite3"))}.sqlite3'
        shutil.copy2('db.sqlite3', backup_name)
        print(f"✅ Database backed up to: {backup_name}")
        return backup_name
    else:
        print("ℹ️  No existing database to backup")
        return None

def clean_migration_files():
    """Clean up problematic migration files"""
    print_section("Cleaning migration files")
    
    # Remove accounts migration files
    accounts_migrations = Path('accounts/migrations')
    if accounts_migrations.exists():
        for file in accounts_migrations.glob('*.py'):
            if file.name != '__init__.py':
                file.unlink()
                print(f"✅ Removed: {file}")
    
    # Remove migration cache
    for app in ['accounts', 'inventory']:
        cache_dir = Path(f'{app}/migrations/__pycache__')
        if cache_dir.exists():
            shutil.rmtree(cache_dir)
            print(f"✅ Removed cache: {cache_dir}")

def create_fresh_database():
    """Create a fresh database"""
    print_section("Creating fresh database")
    
    if os.path.exists('db.sqlite3'):
        os.remove('db.sqlite3')
        print("✅ Removed old database")
    
    print("✅ Ready for fresh database creation")

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n🔧 {description}")
    print(f"   Command: {command}")
    
    result = os.system(command)
    if result == 0:
        print(f"   ✅ Success")
        return True
    else:
        print(f"   ❌ Failed with code: {result}")
        return False

def setup_user_system():
    """Set up the user management system step by step"""
    print_header("FIXING USER MANAGEMENT MIGRATION ISSUES")
    
    print("🔧 This script will:")
    print("   1. Backup existing database")
    print("   2. Clean migration files")
    print("   3. Create fresh database")
    print("   4. Run migrations in correct order")
    print("   5. Set up user system")
    
    # Step 1: Backup database
    backup_file = backup_database()
    
    # Step 2: Clean migration files
    clean_migration_files()
    
    # Step 3: Create fresh database
    create_fresh_database()
    
    # Step 4: Run migrations
    print_section("Running migrations")
    
    # First, create accounts migrations
    if not run_command("python manage.py makemigrations accounts", "Creating accounts migrations"):
        print("❌ Failed to create accounts migrations")
        return False
    
    # Then run all migrations
    if not run_command("python manage.py migrate", "Running all migrations"):
        print("❌ Failed to run migrations")
        return False
    
    # Step 5: Set up user system
    print_section("Setting up user system")
    
    if not run_command("python manage.py setup_user_system", "Setting up user roles and admin"):
        print("⚠️  User system setup failed - will create manually")
        create_manual_setup()
    
    print_header("USER SYSTEM SETUP COMPLETE")
    
    print("✅ Migration issues resolved!")
    print("✅ User management system is ready!")
    
    print_section("Next Steps")
    print("1. Start server: python manage.py runserver")
    print("2. Access login: http://127.0.0.1:8000/accounts/login/")
    print("3. Login with admin credentials")
    print("4. Test user management features")
    
    return True

def create_manual_setup():
    """Manually create basic user setup if command fails"""
    print_section("Manual User Setup")
    
    try:
        # Setup Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
        import django
        django.setup()
        
        from django.contrib.auth import get_user_model
        from accounts.models import UserRole
        
        User = get_user_model()
        
        # Create roles
        roles_data = [
            {
                'name': 'admin',
                'display_name': 'Admin',
                'description': 'Full access to all features including user management.'
            },
            {
                'name': 'automation_user',
                'display_name': 'Automation User',
                'description': 'Read access plus execute access for deployment tasks.'
            },
            {
                'name': 'reader',
                'display_name': 'Reader',
                'description': 'Read-only access to inventory and reports.'
            }
        ]
        
        for role_data in roles_data:
            role, created = UserRole.objects.get_or_create(
                name=role_data['name'],
                defaults=role_data
            )
            if created:
                print(f"✅ Created role: {role.display_name}")
        
        # Create admin user
        admin_role = UserRole.objects.get(name='admin')
        
        if not User.objects.filter(username='admin').exists():
            admin_user = User.objects.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='System',
                last_name='Administrator',
                is_staff=True,
                is_superuser=True,
                is_active=True,
                is_approved=True,
                user_role=admin_role
            )
            print(f"✅ Created admin user: admin / admin123")
        else:
            print("ℹ️  Admin user already exists")
        
        print("✅ Manual setup completed")
        
    except Exception as e:
        print(f"❌ Manual setup failed: {e}")

def test_system():
    """Test the user system"""
    print_section("Testing User System")
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
        import django
        django.setup()
        
        from django.contrib.auth import get_user_model
        from accounts.models import UserRole
        
        User = get_user_model()
        
        # Test models
        role_count = UserRole.objects.count()
        user_count = User.objects.count()
        
        print(f"✅ UserRole model: {role_count} roles")
        print(f"✅ CustomUser model: {user_count} users")
        
        # Test admin user
        if User.objects.filter(username='admin').exists():
            admin = User.objects.get(username='admin')
            print(f"✅ Admin user: {admin.username} ({admin.get_role_display()})")
        
        return True
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def main():
    print_header("USER MANAGEMENT MIGRATION FIX")
    
    print("🚨 Detected migration issue with accounts_customuser table")
    print("🔧 This script will fix the migration problems and set up user management")
    
    response = input("\n❓ Do you want to proceed? This will create a fresh database. (y/N): ")
    
    if response.lower() in ['y', 'yes']:
        if setup_user_system():
            if test_system():
                print_header("SUCCESS - USER MANAGEMENT READY!")
                print("\n🎉 User management system is now working!")
                print("\n📋 What was fixed:")
                print("   ✅ Migration conflicts resolved")
                print("   ✅ Fresh database created")
                print("   ✅ User models created successfully")
                print("   ✅ Default roles and admin user set up")
                print("   ✅ System tested and verified")
                
                print("\n🚀 Ready to use:")
                print("   • Login: http://127.0.0.1:8000/accounts/login/")
                print("   • Admin credentials: admin / admin123")
                print("   • Registration: http://127.0.0.1:8000/accounts/register/")
                
                print("\n🔒 Security features active:")
                print("   • Role-based access control")
                print("   • Account approval workflow")
                print("   • Session tracking")
                print("   • Activity logging")
            else:
                print("⚠️  Setup completed but system test failed")
        else:
            print("❌ Setup failed")
    else:
        print("ℹ️  Setup cancelled")
        
        print_section("Alternative Solutions")
        print("1. Temporarily disable custom user model in settings.py")
        print("2. Use Django's default User model for now")
        print("3. Run this script later when ready")

if __name__ == '__main__':
    main()
