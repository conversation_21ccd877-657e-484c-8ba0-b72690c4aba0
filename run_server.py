#!/usr/bin/env python
"""
Simple server runner for the AWS Inventory Django application
"""
import os
import sys
import django
from django.core.wsgi import get_wsgi_application
from django.core.management import execute_from_command_line

def main():
    """Run the Django development server"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
    
    try:
        from django.core.management import execute_from_command_line
        print("Starting AWS Inventory Django Application...")
        print("Server will be available at: http://127.0.0.1:8000")
        print("Admin interface: http://127.0.0.1:8000/admin")
        print("API endpoints: http://127.0.0.1:8000/api/")
        print("\nPress Ctrl+C to stop the server")
        print("-" * 50)
        
        execute_from_command_line([
            'manage.py', 
            'runserver', 
            '127.0.0.1:8000',
            '--noreload'
        ])
        
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    except KeyboardInterrupt:
        print("\nServer stopped.")

if __name__ == '__main__':
    main()
