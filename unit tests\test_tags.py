#!/usr/bin/env python
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from inventory.models import AWSAccount, EC2Instance, SSMStatus, InstanceTag
from django.test import Client
import json

def test_tags():
    print("=== Testing Enhanced Tag Functionality ===\n")
    
    # Test tag model
    print("1. Tag Model Test:")
    total_tags = InstanceTag.objects.count()
    print(f"   Total unique tags: {total_tags}")
    
    if total_tags > 0:
        print("   Sample tags:")
        for tag in InstanceTag.objects.all()[:10]:
            instances_count = tag.instances.count()
            print(f"     {tag.key}={tag.value} (used by {instances_count} instances)")
    
    # Test tag API
    print("\n2. Tag API Test:")
    client = Client()
    
    response = client.get('/api/available-tags/')
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Available tags API: {data['total_unique_keys']} unique keys")
        print(f"   Sample tag keys: {', '.join(data['tag_keys'][:5])}")
    else:
        print(f"   ❌ Available tags API failed: {response.status_code}")
    
    # Test instance with tags
    print("\n3. Instance-Tag Relationships:")
    instance = EC2Instance.objects.prefetch_related('tags').first()
    if instance:
        tags = instance.tags.all()
        print(f"   Instance {instance.instance_id} has {tags.count()} tags:")
        for tag in tags[:5]:
            print(f"     {tag.key}={tag.value}")
    
    # Test tag search
    print("\n4. Tag Search Test:")
    if total_tags > 0:
        # Get a sample tag
        sample_tag = InstanceTag.objects.first()
        response = client.get(f'/api/ec2-instances/search_by_tags/?tag_{sample_tag.key}={sample_tag.value}')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Tag search for {sample_tag.key}={sample_tag.value}: found {len(data)} instances")
        else:
            print(f"   ❌ Tag search failed: {response.status_code}")
    
    # Test enhanced instance details
    print("\n5. Enhanced Instance Details:")
    response = client.get('/api/ec2-instances/?view=main&page_size=1')
    if response.status_code == 200:
        data = response.json()
        if data['results']:
            instance = data['results'][0]
            print("   ✅ Main inventory view fields:")
            required_fields = [
                'business_unit', 'account_name', 'account_id', 'region',
                'instance_id', 'state', 'name', 'os_information', 
                'private_ip_address', 'instance_type', 'vcpu_count', 
                'memory_gb', 'ssm_status'
            ]
            for field in required_fields:
                value = instance.get(field, 'MISSING')
                print(f"     {field}: {value}")
    
    # Test detailed view
    print("\n6. Detailed Instance View:")
    instance_id = EC2Instance.objects.first().id
    response = client.get(f'/api/ec2-instances/{instance_id}/')
    if response.status_code == 200:
        instance = response.json()
        print("   ✅ Detailed view additional fields:")
        additional_fields = [
            'root_volume_id', 'root_volume_size_gb', 'data_disk_count',
            'env_tag', 'uai_tag', 'patch_tag', 'app_env_cfg_id',
            'maintenance_schedule', 'schedule_tag', 'backup_tag',
            'cto_cloud_ops_managed', 'patch_provider', 'patch_group', 
            'patch_exempt', 'ami_id', 'instance_profile', 'subnet_id', 'subnet_name'
        ]
        for field in additional_fields:
            value = instance.get(field, 'MISSING')
            print(f"     {field}: {value}")
    
    print("\n=== Tag Functionality Test Complete! ===")

if __name__ == '__main__':
    test_tags()
