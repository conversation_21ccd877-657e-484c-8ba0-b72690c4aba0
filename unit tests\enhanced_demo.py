#!/usr/bin/env python
"""
Enhanced Demo for AWS Inventory Django Application
Demonstrates all the new features including flattened tags and enhanced fields
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from inventory.models import AWSAccount, EC2Instance, SSMStatus, InstanceTag
from django.test import Client
import json

def print_header(title):
    print("\n" + "="*70)
    print(f" {title}")
    print("="*70)

def print_section(title):
    print(f"\n--- {title} ---")

def demo_enhanced_features():
    print_header("ENHANCED AWS INVENTORY APPLICATION DEMO")
    
    print("🚀 Enhanced Django AWS Inventory Management System")
    print("   Now with flattened tags, enhanced fields, and advanced search!")
    
    # Data overview
    print_section("Enhanced Data Overview")
    
    accounts = AWSAccount.objects.all()
    instances = EC2Instance.objects.all()
    tags = InstanceTag.objects.all()
    
    print(f"📊 Total AWS Accounts: {accounts.count()}")
    print(f"🖥️  Total EC2 Instances: {instances.count()}")
    print(f"🏷️  Total Unique Tags: {tags.count()}")
    print(f"💓 SSM Status Records: {SSMStatus.objects.count()}")
    
    # Show sample instance with all fields
    print_section("Sample Instance with All Fields")
    instance = instances.first()
    if instance:
        print(f"Instance: {instance.instance_id} ({instance.name})")
        print(f"  Business Unit: {instance.account.business_unit}")
        print(f"  Account: {instance.account.account_name} ({instance.account.account_id})")
        print(f"  Region: {instance.account.region}")
        print(f"  State: {instance.state}")
        print(f"  Type: {instance.instance_type}")
        print(f"  vCPU: {instance.vcpu_count}")
        print(f"  Memory: {instance.memory_gb} GB")
        print(f"  OS: {instance.os_information}")
        print(f"  Private IP: {instance.private_ip_address}")
        print(f"  AMI: {instance.ami_id}")
        print(f"  Root Volume: {instance.root_volume_id} ({instance.root_volume_size_gb} GB)")
        print(f"  Data Disks: {instance.data_disk_count}")
        print(f"  Subnet: {instance.subnet_id} ({instance.subnet_name})")
        print(f"  Instance Profile: {instance.instance_profile}")
        print(f"  Environment: {instance.env_tag}")
        print(f"  UAI: {instance.uai_tag}")
        print(f"  CTO Managed: {instance.cto_cloud_ops_managed}")
        print(f"  Patch Group: {instance.patch_group}")
        
        # Show flattened tags
        instance_tags = instance.tags.all()[:5]
        if instance_tags:
            print(f"  Tags ({instance.tags.count()} total):")
            for tag in instance_tags:
                print(f"    {tag.key}={tag.value}")

def demo_enhanced_api():
    print_header("ENHANCED REST API DEMONSTRATION")
    
    client = Client()
    
    # Test main inventory view
    print_section("Main Inventory Table API")
    response = client.get('/api/ec2-instances/?view=main&page_size=3')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Main inventory view (showing 3 of {data['count']}):")
        for instance in data['results']:
            print(f"   • {instance['instance_id']} | {instance['business_unit']} | {instance['account_name']}")
            print(f"     {instance['instance_type']} | {instance['state']} | {instance['ssm_status'] or 'No SSM'}")
    
    # Test detailed view
    print_section("Detailed Instance View API")
    instance_id = EC2Instance.objects.first().id
    response = client.get(f'/api/ec2-instances/{instance_id}/')
    if response.status_code == 200:
        instance = response.json()
        print(f"✅ Detailed view for {instance['instance_id']}:")
        print(f"   Storage: {instance['root_volume_id']} ({instance['root_volume_size_gb']} GB)")
        print(f"   Network: {instance['subnet_id']} | {instance['private_ip_address']}")
        print(f"   Tags: Env={instance['env_tag']}, UAI={instance['uai_tag']}")
        print(f"   Management: CTO={instance['cto_cloud_ops_managed']}")
    
    # Test available tags API
    print_section("Available Tags API")
    response = client.get('/api/available-tags/')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Available tags: {data['total_unique_keys']} unique keys")
        print(f"   Sample keys: {', '.join(data['tag_keys'][:8])}")
        
        # Show sample tag values
        if 'tags_with_values' in data:
            sample_key = data['tag_keys'][0] if data['tag_keys'] else None
            if sample_key and sample_key in data['tags_with_values']:
                values = data['tags_with_values'][sample_key]
                print(f"   Values for '{sample_key}': {', '.join(values[:5])}")

def demo_tag_search():
    print_header("TAG-BASED SEARCH DEMONSTRATION")
    
    client = Client()
    
    # Test tag filtering
    print_section("Tag-Based Filtering")
    
    # Find instances by Environment tag
    response = client.get('/api/ec2-instances/?tag_key=Environment&tag_value=prod')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Instances with Environment=prod: {data['count']}")
    
    # Find instances by Owner tag
    response = client.get('/api/ec2-instances/?tag_key=Owner')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Instances with Owner tag: {data['count']}")
    
    # Test search functionality
    print_section("Enhanced Search")
    
    # Search by instance ID
    response = client.get('/api/ec2-instances/?search=i-00')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Search 'i-00': {data['count']} instances")
    
    # Search by tag values
    response = client.get('/api/ec2-instances/?search=TeamA')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Search 'TeamA': {data['count']} instances")

def demo_filtering_capabilities():
    print_header("ADVANCED FILTERING CAPABILITIES")
    
    client = Client()
    
    print_section("Multi-Field Filtering")
    
    # Filter by account and state
    account = AWSAccount.objects.first()
    response = client.get(f'/api/ec2-instances/?account_id={account.account_id}&state=running')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Running instances in {account.account_name}: {data['count']}")
    
    # Filter by environment and CTO managed
    response = client.get('/api/ec2-instances/?env_tag=prod&cto_cloud_ops_managed=Yes')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Production instances managed by CTO: {data['count']}")
    
    # Filter by UAI
    response = client.get('/api/ec2-instances/?uai_tag=UAI')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Instances with UAI tags: {data['count']}")

def demo_export_functionality():
    print_header("ENHANCED EXPORT FUNCTIONALITY")
    
    client = Client()
    
    print_section("Excel Export with All Fields")
    
    # Test export endpoint
    response = client.get('/api/ec2-instances/export_excel/')
    if response.status_code == 200:
        print("✅ Excel export successful!")
        print(f"   Content-Type: {response.get('Content-Type')}")
        print(f"   File size: {len(response.content)} bytes")
        print("   Includes all required fields:")
        print("     • BU, Account Name, Account ID, Region")
        print("     • Instance ID, State, Name, OS Information")
        print("     • Private IP, Instance Type, vCPU, Memory")
        print("     • Root EBS Volume ID & Size, Data Disk Count")
        print("     • All tag fields (Env, UAI, CTO Managed, etc.)")
        print("     • AMI, Instance Profile, Security Groups")
        print("     • Subnet ID & Name, SSM Status & Version")
        print("     • All flattened tags")

def main():
    try:
        demo_enhanced_features()
        demo_enhanced_api()
        demo_tag_search()
        demo_filtering_capabilities()
        demo_export_functionality()
        
        print_header("ENHANCED APPLICATION READY!")
        print("🎉 The Enhanced AWS Inventory application is fully functional!")
        
        print("\n📋 Key Enhancements Delivered:")
        print("   ✅ All required fields in database models")
        print("   ✅ Flattened tags as separate key-value relationships")
        print("   ✅ Main inventory table with core fields")
        print("   ✅ Detailed instance view with all fields")
        print("   ✅ Tag-based search and filtering")
        print("   ✅ Enhanced Excel export with all fields")
        print("   ✅ Advanced API endpoints for tag management")
        print("   ✅ Queryable and searchable tag relationships")
        
        print("\n🔍 Search & Filter Capabilities:")
        print("   • Search by Instance ID, Name, IP, AMI, Tags")
        print("   • Filter by Account, State, Environment, UAI")
        print("   • Filter by specific tag key-value pairs")
        print("   • Filter by CTO management status")
        print("   • Combined multi-field filtering")
        
        print("\n📊 Data Structure:")
        print(f"   • {AWSAccount.objects.count()} AWS accounts")
        print(f"   • {EC2Instance.objects.count()} EC2 instances")
        print(f"   • {InstanceTag.objects.count()} unique flattened tags")
        print(f"   • {SSMStatus.objects.count()} SSM status records")
        
        print("\n🚀 Ready to Use:")
        print("   1. Start server: python manage.py runserver")
        print("   2. Access app: http://127.0.0.1:8000")
        print("   3. Test filtering and tag search")
        print("   4. Export detailed Excel reports")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
