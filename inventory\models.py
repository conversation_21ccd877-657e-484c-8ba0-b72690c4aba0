# Import all models from the new modular structure
# This maintains backward compatibility while organizing code better

from .models.base import AWSAccount, InventoryRefreshLog
from .models.ec2 import EC2Instance, InstanceTag, SSMStatus
from .models.eks import EKSCluster, EKSNodeGroup, EKSFargateProfile, EKSClusterTag

# Make all models available at this level for backward compatibility
__all__ = [
    'AWSAccount',
    'InventoryRefreshLog',
    'EC2Instance',
    'InstanceTag',
    'SSMStatus',
    'EKSCluster',
    'EKSNodeGroup',
    'EKSFargateProfile',
    'EKSClusterTag'
]


class InstanceTag(models.Model):
    """Model to store flattened instance tags as key-value pairs"""
    key = models.CharField(max_length=255, db_index=True)
    value = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['key', 'value']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['key', 'value']),
        ]

    def __str__(self):
        return f"{self.key}={self.value}"


class EC2Instance(models.Model):
    """Model to store EC2 instance information with all required fields"""
    STATE_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('shutting-down', 'Shutting Down'),
        ('terminated', 'Terminated'),
        ('stopping', 'Stopping'),
        ('stopped', 'Stopped'),
    ]

    # Core identification
    account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE, related_name='ec2_instances')
    instance_id = models.CharField(max_length=20, db_index=True)
    name = models.CharField(max_length=255, blank=True, db_index=True)
    state = models.CharField(max_length=20, choices=STATE_CHOICES, db_index=True)

    # Instance specifications
    instance_type = models.CharField(max_length=50, db_index=True)
    vcpu_count = models.IntegerField(null=True, blank=True)  # V CPU
    memory_gb = models.FloatField(null=True, blank=True)  # Memory/Ram (GB)

    # Network information
    private_ip_address = models.GenericIPAddressField(null=True, blank=True, db_index=True)
    private_dns_name = models.CharField(max_length=255, blank=True)
    subnet_id = models.CharField(max_length=50, blank=True)
    subnet_name = models.CharField(max_length=255, blank=True)

    # Security and access
    security_groups = models.TextField(blank=True)  # JSON array of security group IDs/names
    instance_profile = models.CharField(max_length=255, blank=True)

    # Storage information
    root_volume_id = models.CharField(max_length=50, blank=True)  # Root EBS Volume ID
    root_volume_size_gb = models.IntegerField(null=True, blank=True)  # Root EBS Volume Size (GB)
    data_disk_count = models.IntegerField(default=0)  # Data Disk Count

    # OS and AMI information
    os_information = models.CharField(max_length=255, blank=True)  # OS Information
    ami_id = models.CharField(max_length=50, blank=True)  # AMI
    platform_type = models.CharField(max_length=50, blank=True)

    # Common tags as direct fields for quick access
    env_tag = models.CharField(max_length=100, blank=True, db_index=True)  # Env Tag
    uai_tag = models.CharField(max_length=100, blank=True, db_index=True)  # Uai Tag
    patch_tag = models.CharField(max_length=100, blank=True)  # Patch Tag
    app_env_cfg_id = models.CharField(max_length=100, blank=True)  # AppEnvCfgID Tag
    maintenance_schedule = models.CharField(max_length=100, blank=True)  # MaintenanceSchedule Tag
    schedule_tag = models.CharField(max_length=100, blank=True)  # Schedule Tag
    backup_tag = models.CharField(max_length=100, blank=True)  # Backup Tag
    cto_cloud_ops_managed = models.CharField(max_length=100, blank=True, db_index=True)  # CTOCloudOpsManaged Tag
    patch_provider = models.CharField(max_length=100, blank=True)  # PatchProvider Tag
    patch_group = models.CharField(max_length=100, blank=True)  # PatchGroup Tag
    patch_exempt = models.CharField(max_length=100, blank=True)  # PatchExempt Tag

    # Flattened tags relationship
    tags = models.ManyToManyField(InstanceTag, blank=True, related_name='instances')
    all_tags_raw = models.TextField(blank=True)  # Raw tags string for backup

    # Metadata
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['account', 'instance_id']
        ordering = ['-last_updated']
        indexes = [
            models.Index(fields=['account', 'instance_id']),
            models.Index(fields=['state', 'instance_type']),
            models.Index(fields=['env_tag', 'uai_tag']),
            models.Index(fields=['cto_cloud_ops_managed']),
        ]

    def __str__(self):
        return f"{self.name or self.instance_id} ({self.instance_type}) - {self.account.account_name}"

    def get_business_unit(self):
        """Get business unit from account"""
        return self.account.business_unit

    def get_account_name(self):
        """Get account name"""
        return self.account.account_name

    def get_account_id(self):
        """Get account ID"""
        return self.account.account_id

    def get_region(self):
        """Get region"""
        return self.account.region


class SSMStatus(models.Model):
    """Model to store SSM agent status information"""
    PING_STATUS_CHOICES = [
        ('Online', 'Online'),
        ('Connection Lost', 'Connection Lost'),
        ('Inactive', 'Inactive'),
        ('Unknown', 'Unknown'),
    ]

    instance = models.OneToOneField(EC2Instance, on_delete=models.CASCADE, related_name='ssm_status')
    ping_status = models.CharField(max_length=20, choices=PING_STATUS_CHOICES, blank=True, db_index=True)  # SSM status
    agent_version = models.CharField(max_length=50, blank=True)  # SSM Agent version
    is_latest_version = models.BooleanField(null=True, blank=True)  # is SSM agent latest
    last_ping_datetime = models.DateTimeField(null=True, blank=True)
    platform_name = models.CharField(max_length=100, blank=True)
    platform_type = models.CharField(max_length=50, blank=True)

    # Status tracking
    ssm_configured = models.BooleanField(default=False)
    last_checked = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-last_checked']
        indexes = [
            models.Index(fields=['ping_status']),
            models.Index(fields=['is_latest_version']),
        ]

    def __str__(self):
        return f"SSM Status for {self.instance.instance_id} - {self.ping_status}"


class EKSClusterTag(models.Model):
    """Model to store flattened EKS cluster tags as key-value pairs"""
    key = models.CharField(max_length=255, db_index=True)
    value = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['key', 'value']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['key', 'value']),
        ]

    def __str__(self):
        return f"{self.key}={self.value}"


class EKSCluster(models.Model):
    """Model to store EKS cluster information with all metadata"""
    STATUS_CHOICES = [
        ('CREATING', 'Creating'),
        ('ACTIVE', 'Active'),
        ('DELETING', 'Deleting'),
        ('FAILED', 'Failed'),
        ('UPDATING', 'Updating'),
    ]

    CLUSTER_TYPE_CHOICES = [
        ('EC2', 'EC2'),
        ('Fargate', 'Fargate'),
        ('Hybrid', 'Hybrid (EC2 + Fargate)'),
    ]

    # Core identification
    account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE, related_name='eks_clusters')
    cluster_name = models.CharField(max_length=255, db_index=True)
    arn = models.CharField(max_length=500, unique=True)

    # Versions and status
    kubernetes_version = models.CharField(max_length=20, blank=True)
    platform_version = models.CharField(max_length=50, blank=True)  # EKS version
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, db_index=True)

    # Cluster configuration
    endpoint = models.URLField(blank=True)
    cluster_type = models.CharField(max_length=20, choices=CLUSTER_TYPE_CHOICES, default='EC2')

    # Network configuration
    vpc_id = models.CharField(max_length=50, blank=True)
    subnet_ids = models.TextField(blank=True)  # JSON array of subnet IDs
    security_group_ids = models.TextField(blank=True)  # JSON array of security group IDs
    endpoint_public_access = models.BooleanField(default=True)
    endpoint_private_access = models.BooleanField(default=False)
    public_access_cidrs = models.TextField(blank=True)  # JSON array of CIDR blocks

    # Service configuration
    service_ipv4_cidr = models.CharField(max_length=50, blank=True)
    service_ipv6_cidr = models.CharField(max_length=50, blank=True)
    ip_family = models.CharField(max_length=10, blank=True)  # ipv4 or ipv6

    # Logging configuration
    logging_enabled = models.BooleanField(default=False)
    logging_types = models.TextField(blank=True)  # JSON array of log types

    # Encryption configuration
    encryption_enabled = models.BooleanField(default=False)
    encryption_key_arn = models.CharField(max_length=500, blank=True)

    # Identity and access
    role_arn = models.CharField(max_length=500, blank=True)
    identity_oidc_issuer = models.URLField(blank=True)

    # Addons and features
    addons = models.TextField(blank=True)  # JSON array of addon names
    fargate_profile_names = models.TextField(blank=True)  # JSON array of Fargate profile names

    # Node groups information (will be populated separately)
    node_groups_count = models.IntegerField(default=0)
    fargate_profiles_count = models.IntegerField(default=0)

    # Flattened tags relationship
    tags = models.ManyToManyField(EKSClusterTag, blank=True, related_name='clusters')
    all_tags_raw = models.TextField(blank=True)  # Raw tags string for backup

    # Timestamps
    created_at_aws = models.DateTimeField(null=True, blank=True)  # When created in AWS
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['account', 'cluster_name']
        ordering = ['-last_updated']
        indexes = [
            models.Index(fields=['account', 'cluster_name']),
            models.Index(fields=['status', 'cluster_type']),
            models.Index(fields=['kubernetes_version']),
        ]

    def __str__(self):
        return f"{self.cluster_name} ({self.kubernetes_version}) - {self.account.account_name}"

    def get_business_unit(self):
        """Get business unit from account"""
        return self.account.business_unit

    def get_account_name(self):
        """Get account name"""
        return self.account.account_name

    def get_account_id(self):
        """Get account ID"""
        return self.account.account_id

    def get_region(self):
        """Get region"""
        return self.account.region


class EKSNodeGroup(models.Model):
    """Model to store EKS node group information"""
    STATUS_CHOICES = [
        ('CREATING', 'Creating'),
        ('ACTIVE', 'Active'),
        ('UPDATING', 'Updating'),
        ('DELETING', 'Deleting'),
        ('CREATE_FAILED', 'Create Failed'),
        ('DELETE_FAILED', 'Delete Failed'),
        ('DEGRADED', 'Degraded'),
    ]

    AMI_TYPE_CHOICES = [
        ('AL2_x86_64', 'Amazon Linux 2 (x86_64)'),
        ('AL2_x86_64_GPU', 'Amazon Linux 2 GPU (x86_64)'),
        ('AL2_ARM_64', 'Amazon Linux 2 (ARM64)'),
        ('CUSTOM', 'Custom'),
        ('BOTTLEROCKET_ARM_64', 'Bottlerocket (ARM64)'),
        ('BOTTLEROCKET_x86_64', 'Bottlerocket (x86_64)'),
    ]

    CAPACITY_TYPE_CHOICES = [
        ('ON_DEMAND', 'On Demand'),
        ('SPOT', 'Spot'),
    ]

    # Core identification
    cluster = models.ForeignKey(EKSCluster, on_delete=models.CASCADE, related_name='node_groups')
    node_group_name = models.CharField(max_length=255, db_index=True)
    arn = models.CharField(max_length=500, unique=True)

    # Status and configuration
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, db_index=True)
    capacity_type = models.CharField(max_length=20, choices=CAPACITY_TYPE_CHOICES, default='ON_DEMAND')

    # Instance configuration
    instance_types = models.TextField(blank=True)  # JSON array of instance types
    ami_type = models.CharField(max_length=50, choices=AMI_TYPE_CHOICES, blank=True)
    node_role_arn = models.CharField(max_length=500, blank=True)

    # Scaling configuration
    desired_size = models.IntegerField(default=0)
    min_size = models.IntegerField(default=0)
    max_size = models.IntegerField(default=0)

    # Network configuration
    subnet_ids = models.TextField(blank=True)  # JSON array of subnet IDs
    remote_access_enabled = models.BooleanField(default=False)
    remote_access_source_sg = models.TextField(blank=True)  # JSON array of security group IDs

    # Launch template
    launch_template_id = models.CharField(max_length=50, blank=True)
    launch_template_version = models.CharField(max_length=20, blank=True)

    # Disk configuration
    disk_size = models.IntegerField(null=True, blank=True)  # In GB

    # Flattened tags relationship
    tags = models.ManyToManyField(EKSClusterTag, blank=True, related_name='node_groups')
    all_tags_raw = models.TextField(blank=True)

    # Timestamps
    created_at_aws = models.DateTimeField(null=True, blank=True)
    modified_at_aws = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['cluster', 'node_group_name']
        ordering = ['-last_updated']
        indexes = [
            models.Index(fields=['cluster', 'node_group_name']),
            models.Index(fields=['status', 'capacity_type']),
        ]

    def __str__(self):
        return f"{self.node_group_name} ({self.cluster.cluster_name}) - {self.status}"


class EKSFargateProfile(models.Model):
    """Model to store EKS Fargate profile information"""
    STATUS_CHOICES = [
        ('CREATING', 'Creating'),
        ('ACTIVE', 'Active'),
        ('DELETING', 'Deleting'),
        ('CREATE_FAILED', 'Create Failed'),
        ('DELETE_FAILED', 'Delete Failed'),
    ]

    # Core identification
    cluster = models.ForeignKey(EKSCluster, on_delete=models.CASCADE, related_name='fargate_profiles')
    profile_name = models.CharField(max_length=255, db_index=True)
    arn = models.CharField(max_length=500, unique=True)

    # Configuration
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, db_index=True)
    pod_execution_role_arn = models.CharField(max_length=500, blank=True)

    # Network configuration
    subnet_ids = models.TextField(blank=True)  # JSON array of subnet IDs

    # Selectors (JSON format)
    selectors = models.TextField(blank=True)  # JSON array of namespace/label selectors

    # Flattened tags relationship
    tags = models.ManyToManyField(EKSClusterTag, blank=True, related_name='fargate_profiles')
    all_tags_raw = models.TextField(blank=True)

    # Timestamps
    created_at_aws = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['cluster', 'profile_name']
        ordering = ['-last_updated']

    def __str__(self):
        return f"{self.profile_name} ({self.cluster.cluster_name}) - {self.status}"


class InventoryRefreshLog(models.Model):
    """Model to track inventory refresh operations"""
    STATUS_CHOICES = [
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE, related_name='refresh_logs')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='running')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    instances_processed = models.IntegerField(default=0)
    errors_count = models.IntegerField(default=0)
    error_details = models.TextField(blank=True)

    class Meta:
        ordering = ['-started_at']

    def __str__(self):
        return f"Refresh {self.account.account_name} - {self.status} ({self.started_at})"
