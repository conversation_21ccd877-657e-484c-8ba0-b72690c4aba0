#!/usr/bin/env python
"""
Demo script for AWS Inventory Django Application
This script demonstrates the key features and functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from inventory.models import AWSAccount, EC2Instance, SSMStatus
from django.test import Client
import json

def print_header(title):
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_section(title):
    print(f"\n--- {title} ---")

def demo_data_overview():
    print_header("AWS INVENTORY APPLICATION DEMO")
    
    print("🚀 Welcome to the AWS Inventory Management System!")
    print("   A comprehensive Django web application for AWS infrastructure management")
    
    print_section("Data Overview")
    
    # Accounts summary
    accounts = AWSAccount.objects.all()
    print(f"📊 Total AWS Accounts: {accounts.count()}")
    
    for account in accounts:
        instance_count = account.ec2_instances.count()
        running_count = account.ec2_instances.filter(state='running').count()
        print(f"   • {account.account_name} ({account.account_id})")
        print(f"     Business Unit: {account.business_unit} | Region: {account.region}")
        print(f"     Instances: {instance_count} total, {running_count} running")
    
    # Instances summary
    instances = EC2Instance.objects.all()
    print(f"\n🖥️  Total EC2 Instances: {instances.count()}")
    
    # State distribution
    states = instances.values_list('state', flat=True)
    state_counts = {}
    for state in states:
        state_counts[state] = state_counts.get(state, 0) + 1
    
    for state, count in state_counts.items():
        print(f"   • {state.title()}: {count}")
    
    # SSM Status summary
    ssm_statuses = SSMStatus.objects.all()
    print(f"\n💓 SSM Agent Status: {ssm_statuses.count()} monitored instances")
    
    ssm_counts = {}
    for status in ssm_statuses.values_list('ping_status', flat=True):
        ssm_counts[status] = ssm_counts.get(status, 0) + 1
    
    for status, count in ssm_counts.items():
        print(f"   • {status}: {count}")

def demo_api_functionality():
    print_header("REST API DEMONSTRATION")
    
    client = Client()
    
    print_section("Dashboard Statistics API")
    response = client.get('/api/dashboard-stats/')
    if response.status_code == 200:
        stats = response.json()
        print("✅ Dashboard API Response:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
    
    print_section("Accounts API")
    response = client.get('/api/accounts/')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Found {len(data['results'])} accounts via API")
        for account in data['results'][:2]:  # Show first 2
            print(f"   • {account['account_name']} - {account['ec2_instances_count']} instances")
    
    print_section("EC2 Instances API")
    response = client.get('/api/ec2-instances/?page_size=5')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Sample instances from API (showing 5 of {data['count']}):")
        for instance in data['results']:
            print(f"   • {instance['instance_id']} ({instance['name'] or 'Unnamed'})")
            print(f"     Type: {instance['instance_type']} | State: {instance['state']}")
            print(f"     Account: {instance['account_name']} | Environment: {instance['env_tag'] or 'N/A'}")
    
    print_section("SSM Status API")
    response = client.get('/api/ssm-status/?page_size=3')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Sample SSM status from API (showing 3 of {data['count']}):")
        for status in data['results']:
            # Get instance details
            instance_response = client.get(f"/api/ec2-instances/{status['instance']}/")
            if instance_response.status_code == 200:
                instance = instance_response.json()
                instance_id = instance.get('instance_id', 'Unknown')
            else:
                instance_id = f"Instance-{status['instance']}"

            print(f"   • {instance_id}: {status['ping_status']}")
            print(f"     Agent Version: {status['agent_version']} | Latest: {status['is_latest_version']}")

def demo_frontend_views():
    print_header("FRONTEND VIEWS DEMONSTRATION")
    
    client = Client()
    
    views = [
        ('/', 'Dashboard'),
        ('/ec2-instances/', 'EC2 Instances'),
        ('/ssm-status/', 'SSM Status'),
        ('/accounts/', 'Account Management'),
    ]
    
    print("🌐 Testing frontend views:")
    
    for url, name in views:
        response = client.get(url)
        status = "✅ Working" if response.status_code == 200 else f"❌ Error ({response.status_code})"
        print(f"   • {name} ({url}): {status}")

def demo_filtering_capabilities():
    print_header("FILTERING & SEARCH CAPABILITIES")
    
    client = Client()
    
    print_section("Filter by Account")
    account = AWSAccount.objects.first()
    response = client.get(f'/api/ec2-instances/?account_id={account.account_id}')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Instances in {account.account_name}: {data['count']}")
    
    print_section("Filter by State")
    response = client.get('/api/ec2-instances/?state=running')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Running instances: {data['count']}")
    
    print_section("Filter by Environment")
    response = client.get('/api/ec2-instances/?env_tag=prod')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Production instances: {data['count']}")
    
    print_section("Search Functionality")
    response = client.get('/api/ec2-instances/?search=Server')
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Instances matching 'Server': {data['count']}")

def demo_sample_data():
    print_header("SAMPLE DATA SHOWCASE")
    
    print_section("Sample EC2 Instance Details")
    instance = EC2Instance.objects.first()
    if instance:
        print(f"Instance ID: {instance.instance_id}")
        print(f"Name: {instance.name}")
        print(f"Type: {instance.instance_type}")
        print(f"State: {instance.state}")
        print(f"Private IP: {instance.private_ip_address}")
        print(f"vCPU: {instance.vcpu_count}")
        print(f"Memory: {instance.memory_gb} GB")
        print(f"OS: {instance.os_information}")
        print(f"Environment: {instance.env_tag}")
        print(f"Account: {instance.account.account_name}")
        
        # SSM Status
        if hasattr(instance, 'ssm_status'):
            ssm = instance.ssm_status
            print(f"SSM Status: {ssm.ping_status}")
            print(f"Agent Version: {ssm.agent_version}")
            print(f"Last Ping: {ssm.last_ping_datetime}")

def main():
    try:
        demo_data_overview()
        demo_api_functionality()
        demo_frontend_views()
        demo_filtering_capabilities()
        demo_sample_data()
        
        print_header("APPLICATION READY!")
        print("🎉 The AWS Inventory application is fully functional!")
        print("\n📋 Next Steps:")
        print("   1. Start the server: python manage.py runserver")
        print("   2. Open browser: http://127.0.0.1:8000")
        print("   3. Explore the dashboard and features")
        print("   4. Access admin panel: http://127.0.0.1:8000/admin")
        print("   5. Test API endpoints: http://127.0.0.1:8000/api/")
        
        print("\n🔧 Key Features Available:")
        print("   • Real-time dashboard with charts")
        print("   • EC2 instance management and filtering")
        print("   • SSM agent status monitoring")
        print("   • AWS account management")
        print("   • Excel export functionality")
        print("   • REST API for automation")
        print("   • Responsive web interface")
        
        print("\n📊 Sample Data Loaded:")
        print(f"   • {AWSAccount.objects.count()} AWS accounts")
        print(f"   • {EC2Instance.objects.count()} EC2 instances")
        print(f"   • {SSMStatus.objects.count()} SSM status records")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
