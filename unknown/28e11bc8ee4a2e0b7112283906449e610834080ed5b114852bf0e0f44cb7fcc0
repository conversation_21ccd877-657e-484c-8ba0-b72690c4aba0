#!/usr/bin/env python3
"""
Django management command to import EKS clusters from AWS
Based on the boto3_scripts/aws_eks_inventory.py script
"""

import json
import boto3
from datetime import datetime
from django.core.management.base import BaseCommand
from django.utils import timezone
from inventory.models import AWSAccount, EKSCluster, EKSNodeGroup, EKSFargateProfile, EKSClusterTag


class Command(BaseCommand):
    help = 'Import EKS clusters from AWS accounts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--account-id',
            type=str,
            help='Import from specific account ID only'
        )
        parser.add_argument(
            '--region',
            type=str,
            help='Import from specific region only'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be imported without actually importing'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting EKS clusters import...'))
        
        # Get accounts to process
        accounts = AWSAccount.objects.all()
        if options['account_id']:
            accounts = accounts.filter(account_id=options['account_id'])
        if options['region']:
            accounts = accounts.filter(region=options['region'])

        total_clusters = 0
        total_node_groups = 0
        total_fargate_profiles = 0

        for account in accounts:
            self.stdout.write(f"\nProcessing account: {account.account_name} ({account.account_id}) in {account.region}")
            
            try:
                clusters, node_groups, fargate_profiles = self.import_account_eks(account, options['dry_run'])
                total_clusters += clusters
                total_node_groups += node_groups
                total_fargate_profiles += fargate_profiles
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"  ✓ Imported {clusters} clusters, {node_groups} node groups, {fargate_profiles} Fargate profiles"
                    )
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"  ✗ Error processing account {account.account_name}: {str(e)}")
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"\n🎉 Import completed! Total: {total_clusters} clusters, {total_node_groups} node groups, {total_fargate_profiles} Fargate profiles"
            )
        )

    def import_account_eks(self, account, dry_run=False):
        """Import EKS clusters for a specific account"""
        
        # Create EKS client
        session = boto3.Session(
            aws_access_key_id=account.access_key_id,
            aws_secret_access_key=account.secret_access_key,
            region_name=account.region
        )
        eks_client = session.client('eks')
        
        clusters_count = 0
        node_groups_count = 0
        fargate_profiles_count = 0

        try:
            # List all clusters
            response = eks_client.list_clusters()
            cluster_names = response.get('clusters', [])
            
            self.stdout.write(f"    Found {len(cluster_names)} clusters")

            for cluster_name in cluster_names:
                try:
                    # Get cluster details
                    cluster_response = eks_client.describe_cluster(name=cluster_name)
                    cluster_data = cluster_response['cluster']
                    
                    if not dry_run:
                        cluster_obj = self.create_or_update_cluster(account, cluster_data)
                        clusters_count += 1
                        
                        # Import node groups
                        ng_count = self.import_node_groups(eks_client, cluster_obj, cluster_name)
                        node_groups_count += ng_count
                        
                        # Import Fargate profiles
                        fp_count = self.import_fargate_profiles(eks_client, cluster_obj, cluster_name)
                        fargate_profiles_count += fp_count
                        
                        # Update counts
                        cluster_obj.node_groups_count = ng_count
                        cluster_obj.fargate_profiles_count = fp_count
                        cluster_obj.save()
                    else:
                        clusters_count += 1
                        self.stdout.write(f"      [DRY RUN] Would import cluster: {cluster_name}")

                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f"      ⚠ Error processing cluster {cluster_name}: {str(e)}")
                    )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"    ✗ Error listing clusters: {str(e)}")
            )

        return clusters_count, node_groups_count, fargate_profiles_count

    def create_or_update_cluster(self, account, cluster_data):
        """Create or update EKS cluster"""
        
        cluster_name = cluster_data['name']
        arn = cluster_data['arn']
        
        # Get or create cluster
        cluster, created = EKSCluster.objects.get_or_create(
            account=account,
            cluster_name=cluster_name,
            defaults={'arn': arn}
        )
        
        # Update cluster fields
        cluster.arn = arn
        cluster.kubernetes_version = cluster_data.get('version', '')
        cluster.platform_version = cluster_data.get('platformVersion', '')
        cluster.status = cluster_data.get('status', '')
        cluster.endpoint = cluster_data.get('endpoint', '')
        
        # Network configuration
        if 'resourcesVpcConfig' in cluster_data:
            vpc_config = cluster_data['resourcesVpcConfig']
            cluster.vpc_id = vpc_config.get('vpcId', '')
            cluster.subnet_ids = json.dumps(vpc_config.get('subnetIds', []))
            cluster.security_group_ids = json.dumps(vpc_config.get('securityGroupIds', []))
            cluster.endpoint_public_access = vpc_config.get('endpointPublicAccess', True)
            cluster.endpoint_private_access = vpc_config.get('endpointPrivateAccess', False)
            cluster.public_access_cidrs = json.dumps(vpc_config.get('publicAccessCidrs', []))
        
        # Service configuration
        if 'kubernetesNetworkConfig' in cluster_data:
            network_config = cluster_data['kubernetesNetworkConfig']
            cluster.service_ipv4_cidr = network_config.get('serviceIpv4Cidr', '')
            cluster.service_ipv6_cidr = network_config.get('serviceIpv6Cidr', '')
            cluster.ip_family = network_config.get('ipFamily', '')
        
        # Logging configuration
        if 'logging' in cluster_data:
            logging_config = cluster_data['logging']
            cluster.logging_enabled = logging_config.get('clusterLogging', {}).get('enabled', False)
            log_types = []
            for log_setup in logging_config.get('clusterLogging', {}).get('types', []):
                if log_setup.get('enabled'):
                    log_types.extend(log_setup.get('types', []))
            cluster.logging_types = json.dumps(log_types)
        
        # Encryption configuration
        if 'encryptionConfig' in cluster_data:
            encryption_configs = cluster_data['encryptionConfig']
            if encryption_configs:
                cluster.encryption_enabled = True
                cluster.encryption_key_arn = encryption_configs[0].get('provider', {}).get('keyArn', '')
        
        # Identity and access
        cluster.role_arn = cluster_data.get('roleArn', '')
        if 'identity' in cluster_data and 'oidc' in cluster_data['identity']:
            cluster.identity_oidc_issuer = cluster_data['identity']['oidc'].get('issuer', '')
        
        # Timestamps
        if 'createdAt' in cluster_data:
            cluster.created_at_aws = cluster_data['createdAt']
        
        # Tags
        tags_raw = cluster_data.get('tags', {})
        cluster.all_tags_raw = json.dumps(tags_raw)
        
        cluster.save()
        
        # Process tags
        self.process_cluster_tags(cluster, tags_raw)
        
        # Determine cluster type based on node groups and Fargate profiles
        # This will be updated after importing node groups and Fargate profiles
        
        return cluster

    def process_cluster_tags(self, cluster, tags_dict):
        """Process and store cluster tags"""
        cluster.tags.clear()
        
        for key, value in tags_dict.items():
            tag, created = EKSClusterTag.objects.get_or_create(
                key=key,
                value=str(value)
            )
            cluster.tags.add(tag)

    def import_node_groups(self, eks_client, cluster, cluster_name):
        """Import node groups for a cluster"""
        try:
            response = eks_client.list_nodegroups(clusterName=cluster_name)
            node_group_names = response.get('nodegroups', [])
            
            for ng_name in node_group_names:
                try:
                    ng_response = eks_client.describe_nodegroup(
                        clusterName=cluster_name,
                        nodegroupName=ng_name
                    )
                    ng_data = ng_response['nodegroup']
                    
                    # Create or update node group
                    node_group, created = EKSNodeGroup.objects.get_or_create(
                        cluster=cluster,
                        node_group_name=ng_name,
                        defaults={'arn': ng_data['nodegroupArn']}
                    )
                    
                    # Update node group fields
                    node_group.arn = ng_data['nodegroupArn']
                    node_group.status = ng_data.get('status', '')
                    node_group.capacity_type = ng_data.get('capacityType', 'ON_DEMAND')
                    node_group.instance_types = json.dumps(ng_data.get('instanceTypes', []))
                    node_group.ami_type = ng_data.get('amiType', '')
                    node_group.node_role_arn = ng_data.get('nodeRole', '')
                    
                    # Scaling configuration
                    if 'scalingConfig' in ng_data:
                        scaling = ng_data['scalingConfig']
                        node_group.desired_size = scaling.get('desiredSize', 0)
                        node_group.min_size = scaling.get('minSize', 0)
                        node_group.max_size = scaling.get('maxSize', 0)
                    
                    # Network configuration
                    if 'subnets' in ng_data:
                        node_group.subnet_ids = json.dumps(ng_data['subnets'])
                    
                    # Remote access
                    if 'remoteAccess' in ng_data:
                        remote_access = ng_data['remoteAccess']
                        node_group.remote_access_enabled = True
                        if 'sourceSecurityGroups' in remote_access:
                            node_group.remote_access_source_sg = json.dumps(remote_access['sourceSecurityGroups'])
                    
                    # Launch template
                    if 'launchTemplate' in ng_data:
                        lt = ng_data['launchTemplate']
                        node_group.launch_template_id = lt.get('id', '')
                        node_group.launch_template_version = lt.get('version', '')
                    
                    # Disk configuration
                    if 'diskSize' in ng_data:
                        node_group.disk_size = ng_data['diskSize']
                    
                    # Timestamps
                    if 'createdAt' in ng_data:
                        node_group.created_at_aws = ng_data['createdAt']
                    if 'modifiedAt' in ng_data:
                        node_group.modified_at_aws = ng_data['modifiedAt']
                    
                    # Tags
                    tags_raw = ng_data.get('tags', {})
                    node_group.all_tags_raw = json.dumps(tags_raw)
                    
                    node_group.save()
                    
                    # Process tags
                    self.process_node_group_tags(node_group, tags_raw)
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f"        ⚠ Error processing node group {ng_name}: {str(e)}")
                    )
            
            return len(node_group_names)
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"      ⚠ Error listing node groups: {str(e)}")
            )
            return 0

    def process_node_group_tags(self, node_group, tags_dict):
        """Process and store node group tags"""
        node_group.tags.clear()
        
        for key, value in tags_dict.items():
            tag, created = EKSClusterTag.objects.get_or_create(
                key=key,
                value=str(value)
            )
            node_group.tags.add(tag)

    def import_fargate_profiles(self, eks_client, cluster, cluster_name):
        """Import Fargate profiles for a cluster"""
        try:
            response = eks_client.list_fargate_profiles(clusterName=cluster_name)
            profile_names = response.get('fargateProfileNames', [])
            
            for profile_name in profile_names:
                try:
                    fp_response = eks_client.describe_fargate_profile(
                        clusterName=cluster_name,
                        fargateProfileName=profile_name
                    )
                    fp_data = fp_response['fargateProfile']
                    
                    # Create or update Fargate profile
                    fargate_profile, created = EKSFargateProfile.objects.get_or_create(
                        cluster=cluster,
                        profile_name=profile_name,
                        defaults={'arn': fp_data['fargateProfileArn']}
                    )
                    
                    # Update Fargate profile fields
                    fargate_profile.arn = fp_data['fargateProfileArn']
                    fargate_profile.status = fp_data.get('status', '')
                    fargate_profile.pod_execution_role_arn = fp_data.get('podExecutionRoleArn', '')
                    
                    # Network configuration
                    if 'subnets' in fp_data:
                        fargate_profile.subnet_ids = json.dumps(fp_data['subnets'])
                    
                    # Selectors
                    if 'selectors' in fp_data:
                        fargate_profile.selectors = json.dumps(fp_data['selectors'])
                    
                    # Timestamps
                    if 'createdAt' in fp_data:
                        fargate_profile.created_at_aws = fp_data['createdAt']
                    
                    # Tags
                    tags_raw = fp_data.get('tags', {})
                    fargate_profile.all_tags_raw = json.dumps(tags_raw)
                    
                    fargate_profile.save()
                    
                    # Process tags
                    self.process_fargate_profile_tags(fargate_profile, tags_raw)
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f"        ⚠ Error processing Fargate profile {profile_name}: {str(e)}")
                    )
            
            return len(profile_names)
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"      ⚠ Error listing Fargate profiles: {str(e)}")
            )
            return 0

    def process_fargate_profile_tags(self, fargate_profile, tags_dict):
        """Process and store Fargate profile tags"""
        fargate_profile.tags.clear()
        
        for key, value in tags_dict.items():
            tag, created = EKSClusterTag.objects.get_or_create(
                key=key,
                value=str(value)
            )
            fargate_profile.tags.add(tag)
