{% extends 'inventory/base.html' %}

{% block title %}EKS Clusters - Cloud Operations Central{% endblock %}
{% block page_title %}EKS Clusters{% endblock %}

{% block page_actions %}
<div class="btn-group me-2">
    <button type="button" class="btn btn-outline-primary" onclick="exportToExcel()">
        <i class="fas fa-file-excel me-1"></i>
        Export Excel
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="loadClusters()">
        <i class="fas fa-sync-alt me-1"></i>
        Refresh
    </button>
</div>
<button type="button" class="btn btn-success" onclick="refreshAllInventory()">
    <i class="fas fa-sync-alt me-1"></i>
    Refresh All
</button>
{% endblock %}

{% block content %}
<!-- Compact Filters -->
<div class="filter-section compact-form">
    <div class="row g-2">
        <div class="col-md-2">
            <select class="form-select form-select-sm" id="filter-bu">
                <option value="">All Business Units</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select form-select-sm" id="filter-account">
                <option value="">All Accounts</option>
            </select>
        </div>
        <div class="col-md-1">
            <select class="form-select form-select-sm" id="filter-status">
                <option value="">Status</option>
                <option value="ACTIVE">Active</option>
                <option value="CREATING">Creating</option>
                <option value="DELETING">Deleting</option>
                <option value="FAILED">Failed</option>
                <option value="UPDATING">Updating</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select form-select-sm" id="filter-cluster-type">
                <option value="">Cluster Type</option>
                <option value="EC2">EC2</option>
                <option value="Fargate">Fargate</option>
                <option value="Hybrid">Hybrid</option>
            </select>
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control form-control-sm" id="filter-k8s-version" placeholder="K8s Version">
        </div>
        <div class="col-md-2">
            <input type="text" class="form-control form-control-sm" id="filter-search" placeholder="Search clusters...">
        </div>
        <div class="col-md-1">
            <button type="button" class="btn btn-primary btn-sm w-100" onclick="applyFilters()" title="Apply Filters">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
    <div class="row g-2 mt-1">
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                <i class="fas fa-times me-1"></i>Clear
            </button>
        </div>
    </div>
</div>

<!-- Compact Clusters Table -->
<div class="card compact-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <span class="fw-bold">EKS Clusters</span>
        <span class="badge bg-primary" id="cluster-count">0 clusters</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-compact table-striped table-hover mb-0" id="clusters-table">
                <thead>
                    <tr>
                        <th style="width: 140px;">Cluster Name</th>
                        <th style="width: 100px;">K8s Version</th>
                        <th style="width: 80px;">Status</th>
                        <th style="width: 80px;">Type</th>
                        <th style="width: 100px;">Account</th>
                        <th style="width: 80px;">Node Groups</th>
                        <th style="width: 80px;">Fargate</th>
                        <th style="width: 120px;">Addons</th>
                        <th style="width: 100px;">Created</th>
                        <th style="width: 60px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>

        <!-- Compact Pagination -->
        <div class="p-2 border-top bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted" id="pagination-info">Showing 0 of 0 clusters</small>
                <nav aria-label="Clusters pagination">
                    <ul class="pagination pagination-sm mb-0" id="pagination">
                        <!-- Pagination will be generated dynamically -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Compact Cluster Detail Modal -->
<div class="modal fade" id="clusterModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header py-2">
                <h6 class="modal-title">Cluster Details</h6>
                <button type="button" class="btn-close btn-sm" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-3" id="cluster-details">
                <!-- Cluster details will be loaded here -->
            </div>
            <div class="modal-footer py-2">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let currentFilters = {};

    function loadClusters(page = 1) {
        showLoading();
        
        let url = `/api/eks-clusters/?page=${page}`;
        
        // Add filters to URL
        Object.keys(currentFilters).forEach(key => {
            if (currentFilters[key]) {
                url += `&${key}=${encodeURIComponent(currentFilters[key])}`;
            }
        });
        
        $.ajax({
            url: url,
            method: 'GET',
            success: function(data) {
                updateClustersTable(data.results);
                updatePagination(data);
                updateClusterCount(data.count);
                hideLoading();
            },
            error: function(xhr) {
                hideLoading();
                showAlert('Failed to load EKS clusters', 'danger');
            }
        });
    }

    function updateClustersTable(clusters) {
        const tbody = $('#clusters-table tbody');
        tbody.empty();

        if (clusters && clusters.length > 0) {
            clusters.forEach(function(cluster) {
                const addonsText = cluster.addons_list ? 
                    (cluster.addons_list.length > 2 ? 
                        cluster.addons_list.slice(0, 2).join(', ') + '...' : 
                        cluster.addons_list.join(', ')) : '-';
                
                const createdDate = cluster.created_at_aws ? 
                    new Date(cluster.created_at_aws).toLocaleDateString() : '-';

                const row = `
                    <tr>
                        <td class="text-truncate" style="max-width: 140px;" title="${cluster.cluster_name}">${truncateText(cluster.cluster_name, 18)}</td>
                        <td><small>${cluster.kubernetes_version || '-'}</small></td>
                        <td><span class="badge badge-sm bg-${getStatusColor(cluster.status)}">${cluster.status}</span></td>
                        <td><small>${cluster.cluster_type || '-'}</small></td>
                        <td class="text-truncate" style="max-width: 100px;" title="${cluster.account_name}">${truncateText(cluster.account_name, 12)}</td>
                        <td><span class="badge bg-info">${cluster.node_groups_count || 0}</span></td>
                        <td><span class="badge bg-secondary">${cluster.fargate_profiles_count || 0}</span></td>
                        <td class="text-truncate" style="max-width: 120px;" title="${addonsText}">${truncateText(addonsText, 15)}</td>
                        <td><small>${createdDate}</small></td>
                        <td>
                            <button class="btn btn-outline-primary btn-sm" onclick="viewClusterDetails(${cluster.id})" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        } else {
            tbody.append('<tr><td colspan="10" class="text-center text-muted">No EKS clusters found</td></tr>');
        }
    }

    function truncateText(text, maxLength) {
        if (!text || text === '-') return text;
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    function getStatusColor(status) {
        switch(status) {
            case 'ACTIVE': return 'success';
            case 'CREATING': return 'warning';
            case 'UPDATING': return 'info';
            case 'DELETING': return 'danger';
            case 'FAILED': return 'danger';
            default: return 'secondary';
        }
    }

    function updatePagination(data) {
        const pagination = $('#pagination');
        const paginationInfo = $('#pagination-info');
        pagination.empty();

        const pageSize = 50;
        const startItem = ((currentPage - 1) * pageSize) + 1;
        const endItem = Math.min(currentPage * pageSize, data.count);

        paginationInfo.text(`Showing ${startItem}-${endItem} of ${data.count} clusters`);

        if (data.count > pageSize) {
            const totalPages = Math.ceil(data.count / pageSize);

            // Previous button
            if (data.previous) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link page-link-sm" href="#" onclick="loadClusters(${currentPage - 1})">&laquo;</a>
                    </li>
                `);
            }

            // Page numbers (show max 3 pages around current for compact view)
            const startPage = Math.max(1, currentPage - 1);
            const endPage = Math.min(totalPages, currentPage + 1);

            if (startPage > 1) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link page-link-sm" href="#" onclick="loadClusters(1)">1</a>
                    </li>
                `);
                if (startPage > 2) {
                    pagination.append(`<li class="page-item disabled"><span class="page-link page-link-sm">...</span></li>`);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link page-link-sm" href="#" onclick="loadClusters(${i})">${i}</a>
                    </li>
                `);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pagination.append(`<li class="page-item disabled"><span class="page-link page-link-sm">...</span></li>`);
                }
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link page-link-sm" href="#" onclick="loadClusters(${totalPages})">${totalPages}</a>
                    </li>
                `);
            }

            // Next button
            if (data.next) {
                pagination.append(`
                    <li class="page-item">
                        <a class="page-link page-link-sm" href="#" onclick="loadClusters(${currentPage + 1})">&raquo;</a>
                    </li>
                `);
            }
        }
    }

    function updateClusterCount(count) {
        $('#cluster-count').text(`${count} clusters`);
    }

    // Store all accounts data for cascading filters
    let allAccountsData = [];

    function loadFilterOptions() {
        // Load business units and accounts for filter
        $.ajax({
            url: '/api/accounts/',
            method: 'GET',
            data: { page_size: 1000 }, // Get all accounts
            success: function(data) {
                allAccountsData = data.results; // Store for cascading

                const buSelect = $('#filter-bu');

                // Extract unique business units
                const businessUnits = [...new Set(data.results.map(account => account.business_unit))].sort();
                businessUnits.forEach(function(bu) {
                    buSelect.append(`<option value="${bu}">${bu}</option>`);
                });

                // Initially populate all accounts
                updateAccountDropdown();

                // Set up cascading filter event
                buSelect.on('change', function() {
                    updateAccountDropdown();
                    // Auto-apply filters when BU changes
                    applyFilters();
                });
            }
        });
    }

    function updateAccountDropdown() {
        const selectedBU = $('#filter-bu').val();
        const accountSelect = $('#filter-account');

        // Clear current options except the first one
        accountSelect.find('option:not(:first)').remove();

        // Filter accounts based on selected BU
        let accountsToShow = allAccountsData;
        if (selectedBU) {
            accountsToShow = allAccountsData.filter(account => account.business_unit === selectedBU);
        }

        // Update the placeholder text based on selection
        const firstOption = accountSelect.find('option:first');
        if (selectedBU) {
            firstOption.text(`All ${selectedBU} Accounts`);
        } else {
            firstOption.text('All Accounts');
        }

        // Populate filtered accounts
        accountsToShow.forEach(function(account) {
            const displayName = selectedBU ?
                account.account_name : // Show just name if BU is selected
                `${account.account_name} (${account.business_unit})`; // Show with BU if all BUs

            accountSelect.append(`<option value="${account.account_id}">${displayName}</option>`);
        });

        // Reset account selection when BU changes
        accountSelect.val('');

        // Show visual feedback
        if (selectedBU) {
            accountSelect.addClass('border-primary');
            setTimeout(() => accountSelect.removeClass('border-primary'), 1000);
        }
    }

    function applyFilters() {
        currentFilters = {
            business_unit: $('#filter-bu').val(),
            account_id: $('#filter-account').val(),
            status: $('#filter-status').val(),
            cluster_type: $('#filter-cluster-type').val(),
            kubernetes_version: $('#filter-k8s-version').val(),
            search: $('#filter-search').val()
        };
        currentPage = 1;
        loadClusters(1);
    }

    function clearFilters() {
        $('#filter-bu').val('');
        $('#filter-account').val('');
        $('#filter-status').val('');
        $('#filter-cluster-type').val('');
        $('#filter-k8s-version').val('');
        $('#filter-search').val('');
        currentFilters = {};
        currentPage = 1;
        loadClusters(1);
    }

    function exportToExcel() {
        showLoading();

        let url = '/api/eks-clusters/export_excel/';

        // Add current filters to export
        const params = new URLSearchParams(currentFilters);
        if (params.toString()) {
            url += '?' + params.toString();
        }

        window.location.href = url;
        hideLoading();
    }

    function viewClusterDetails(clusterId) {
        $.ajax({
            url: `/api/eks-clusters/${clusterId}/`,
            method: 'GET',
            success: function(cluster) {
                const detailsHtml = `
                    <div class="row g-2">
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header py-1"><small class="fw-bold">Basic Information</small></div>
                                <div class="card-body p-2">
                                    <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                        <tr><td class="fw-bold" style="width: 40%;">Cluster Name:</td><td><code>${cluster.cluster_name}</code></td></tr>
                                        <tr><td class="fw-bold">K8s Version:</td><td>${cluster.kubernetes_version || '-'}</td></tr>
                                        <tr><td class="fw-bold">EKS Version:</td><td>${cluster.platform_version || '-'}</td></tr>
                                        <tr><td class="fw-bold">Status:</td><td><span class="badge bg-${getStatusColor(cluster.status)}">${cluster.status}</span></td></tr>
                                        <tr><td class="fw-bold">Type:</td><td>${cluster.cluster_type || '-'}</td></tr>
                                        <tr><td class="fw-bold">Endpoint:</td><td><small>${cluster.endpoint || '-'}</small></td></tr>
                                        <tr><td class="fw-bold">Created:</td><td>${cluster.created_at_aws ? new Date(cluster.created_at_aws).toLocaleString() : '-'}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header py-1"><small class="fw-bold">Network Configuration</small></div>
                                <div class="card-body p-2">
                                    <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                        <tr><td class="fw-bold" style="width: 40%;">VPC ID:</td><td><code>${cluster.vpc_id || '-'}</code></td></tr>
                                        <tr><td class="fw-bold">Public Access:</td><td>${cluster.endpoint_public_access ? 'Yes' : 'No'}</td></tr>
                                        <tr><td class="fw-bold">Private Access:</td><td>${cluster.endpoint_private_access ? 'Yes' : 'No'}</td></tr>
                                        <tr><td class="fw-bold">Service IPv4 CIDR:</td><td>${cluster.service_ipv4_cidr || '-'}</td></tr>
                                        <tr><td class="fw-bold">IP Family:</td><td>${cluster.ip_family || '-'}</td></tr>
                                        <tr><td class="fw-bold">Subnets:</td><td><small>${cluster.subnet_ids_list ? cluster.subnet_ids_list.join(', ') : '-'}</small></td></tr>
                                        <tr><td class="fw-bold">Security Groups:</td><td><small>${cluster.security_group_ids_list ? cluster.security_group_ids_list.join(', ') : '-'}</small></td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header py-1"><small class="fw-bold">Features & Security</small></div>
                                <div class="card-body p-2">
                                    <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                        <tr><td class="fw-bold" style="width: 40%;">Logging:</td><td>${cluster.logging_enabled ? 'Enabled' : 'Disabled'}</td></tr>
                                        <tr><td class="fw-bold">Log Types:</td><td><small>${cluster.logging_types_list ? cluster.logging_types_list.join(', ') : '-'}</small></td></tr>
                                        <tr><td class="fw-bold">Encryption:</td><td>${cluster.encryption_enabled ? 'Enabled' : 'Disabled'}</td></tr>
                                        <tr><td class="fw-bold">Role ARN:</td><td><small>${cluster.role_arn || '-'}</small></td></tr>
                                        <tr><td class="fw-bold">OIDC Issuer:</td><td><small>${cluster.identity_oidc_issuer || '-'}</small></td></tr>
                                        <tr><td class="fw-bold">Node Groups:</td><td><span class="badge bg-info">${cluster.node_groups_count || 0}</span></td></tr>
                                        <tr><td class="fw-bold">Fargate Profiles:</td><td><span class="badge bg-secondary">${cluster.fargate_profiles_count || 0}</span></td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-2 mt-2">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">Addons</small></div>
                                <div class="card-body p-2">
                                    ${cluster.addons_list && cluster.addons_list.length > 0 ?
                                        cluster.addons_list.map(addon => `<span class="badge bg-primary me-1">${addon}</span>`).join('') :
                                        '<span class="text-muted">No addons installed</span>'
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">Account Information</small></div>
                                <div class="card-body p-2">
                                    <table class="table table-sm table-borderless mb-0" style="font-size: 0.8rem;">
                                        <tr><td class="fw-bold" style="width: 30%;">Business Unit:</td><td>${cluster.business_unit || '-'}</td></tr>
                                        <tr><td class="fw-bold">Account:</td><td>${cluster.account_name || '-'}</td></tr>
                                        <tr><td class="fw-bold">Account ID:</td><td><code>${cluster.account_id || '-'}</code></td></tr>
                                        <tr><td class="fw-bold">Region:</td><td>${cluster.region || '-'}</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    ${cluster.node_groups && cluster.node_groups.length > 0 ? `
                    <div class="row g-2 mt-2">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">Node Groups (${cluster.node_groups.length})</small></div>
                                <div class="card-body p-2">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped mb-0" style="font-size: 0.75rem;">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Status</th>
                                                    <th>Instance Types</th>
                                                    <th>Capacity</th>
                                                    <th>Scaling</th>
                                                    <th>AMI Type</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${cluster.node_groups.map(ng => `
                                                    <tr>
                                                        <td>${ng.node_group_name}</td>
                                                        <td><span class="badge badge-sm bg-${getStatusColor(ng.status)}">${ng.status}</span></td>
                                                        <td><small>${ng.instance_types_list ? ng.instance_types_list.join(', ') : '-'}</small></td>
                                                        <td>${ng.capacity_type}</td>
                                                        <td><small>${ng.min_size}/${ng.desired_size}/${ng.max_size}</small></td>
                                                        <td><small>${ng.ami_type || '-'}</small></td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    ${cluster.fargate_profiles && cluster.fargate_profiles.length > 0 ? `
                    <div class="row g-2 mt-2">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">Fargate Profiles (${cluster.fargate_profiles.length})</small></div>
                                <div class="card-body p-2">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped mb-0" style="font-size: 0.75rem;">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Status</th>
                                                    <th>Execution Role</th>
                                                    <th>Subnets</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${cluster.fargate_profiles.map(fp => `
                                                    <tr>
                                                        <td>${fp.profile_name}</td>
                                                        <td><span class="badge badge-sm bg-${getStatusColor(fp.status)}">${fp.status}</span></td>
                                                        <td><small>${fp.pod_execution_role_arn || '-'}</small></td>
                                                        <td><small>${fp.subnet_ids_list ? fp.subnet_ids_list.join(', ') : '-'}</small></td>
                                                    </tr>
                                                `).join('')}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    ${cluster.cluster_tags && cluster.cluster_tags.length > 0 ? `
                    <div class="row g-2 mt-2">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">Tags (${cluster.cluster_tags.length})</small></div>
                                <div class="card-body p-2">
                                    <div class="row g-1">
                                        ${cluster.cluster_tags.map(tag => `
                                            <div class="col-md-6">
                                                <small><strong>${tag.key}:</strong> ${tag.value}</small>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    <div class="row g-2 mt-2">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header py-1"><small class="fw-bold">ARN</small></div>
                                <div class="card-body p-2">
                                    <code style="font-size: 0.75rem; word-break: break-all;">${cluster.arn || '-'}</code>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('#cluster-details').html(detailsHtml);
                $('#clusterModal').modal('show');
            },
            error: function(xhr) {
                showAlert('Failed to load cluster details', 'danger');
            }
        });
    }

    // Load data when page loads
    $(document).ready(function() {
        loadFilterOptions();
        loadClusters();

        // Set up search field with debounced auto-search
        const searchInput = document.getElementById('filter-search');
        const debouncedSearch = debounce(function() {
            if (searchInput.value.length >= 2 || searchInput.value.length === 0) {
                applyFilters();
            }
        }, 500);

        searchInput.addEventListener('input', debouncedSearch);
    });

    // Make loadData available globally for base template
    window.loadData = loadClusters;
</script>
{% endblock %}
