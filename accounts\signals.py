from django.db.models.signals import post_save, post_migrate
from django.dispatch import receiver
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.contrib.sessions.models import Session
from django.utils import timezone
from .models import CustomUser, UserRole, UserSession, UserActivity


@receiver(post_migrate)
def create_default_roles(sender, **kwargs):
    """Create default roles after migration"""
    if sender.name == 'accounts':
        UserRole.get_default_roles()


@receiver(post_save, sender=CustomUser)
def assign_default_role(sender, instance, created, **kwargs):
    """Assign default role to new users"""
    if created and not instance.user_role:
        # Assign reader role by default
        reader_role, _ = UserRole.objects.get_or_create(
            name='reader',
            defaults={
                'display_name': 'Reader',
                'description': 'Read-only access to inventory, automation reports, and decommission reports.'
            }
        )
        instance.user_role = reader_role
        instance.save()


@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log user login and create session record"""
    ip_address = get_client_ip(request)
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    # Record login in user model (only if custom user model)
    if hasattr(user, 'record_login'):
        user.record_login(ip_address)
    
    # Create session record (only if table exists)
    session_key = request.session.session_key
    if session_key:
        try:
            UserSession.objects.update_or_create(
                session_key=session_key,
                defaults={
                    'user': user,
                    'ip_address': ip_address,
                    'user_agent': user_agent,
                    'is_active': True
                }
            )
        except Exception:
            # UserSession table doesn't exist (migration not run)
            pass
    
    # Log activity (only if table exists)
    try:
        UserActivity.objects.create(
            user=user,
            action='login',
            description=f'User logged in from {ip_address}',
            ip_address=ip_address,
            user_agent=user_agent
        )
    except Exception:
        # UserActivity table doesn't exist (migration not run)
        pass


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout and end session"""
    if user and user.is_authenticated:
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # End session record (only if table exists)
        session_key = request.session.session_key
        if session_key:
            try:
                user_session = UserSession.objects.get(session_key=session_key, is_active=True)
                user_session.end_session()
            except Exception:
                # UserSession table doesn't exist or session not found
                pass
        
        # Log activity (only if table exists)
        try:
            UserActivity.objects.create(
                user=user,
                action='logout',
                description=f'User logged out from {ip_address}',
                ip_address=ip_address,
                user_agent=user_agent
            )
        except Exception:
            # UserActivity table doesn't exist (migration not run)
            pass


@receiver(user_login_failed)
def log_failed_login(sender, credentials, request, **kwargs):
    """Log failed login attempts"""
    username = credentials.get('username')
    ip_address = get_client_ip(request)
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        user = User.objects.get(username=username)
        if hasattr(user, 'record_failed_login'):
            user.record_failed_login()
        
        # Log activity (only if table exists)
        try:
            UserActivity.objects.create(
                user=user,
                action='failed_login',
                description=f'Failed login attempt from {ip_address}',
                ip_address=ip_address,
                user_agent=user_agent,
                additional_data={'username': username}
            )
        except Exception:
            pass

        # Log account lock if applicable (only if custom user model)
        if hasattr(user, 'is_account_locked') and user.is_account_locked():
            try:
                UserActivity.objects.create(
                    user=user,
                    action='account_locked',
                    description=f'Account locked due to multiple failed login attempts from {ip_address}',
                    ip_address=ip_address,
                    user_agent=user_agent,
                    additional_data={'failed_attempts': getattr(user, 'failed_login_attempts', 0)}
                )
            except Exception:
                pass
    except User.DoesNotExist:
        # Log failed login for non-existent user (only if table exists)
        try:
            UserActivity.objects.create(
                user=None,
                action='failed_login',
                description=f'Failed login attempt for non-existent user: {username} from {ip_address}',
                ip_address=ip_address,
                user_agent=user_agent,
                additional_data={'username': username}
            )
        except Exception:
            pass


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def log_user_activity(user, action, description, request=None, additional_data=None):
    """Helper function to log user activities"""
    ip_address = None
    user_agent = ''
    
    if request:
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
    
    try:
        UserActivity.objects.create(
            user=user,
            action=action,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            additional_data=additional_data or {}
        )
    except Exception:
        # UserActivity table doesn't exist (migration not run)
        pass
