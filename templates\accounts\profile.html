{% extends 'inventory/base.html' %}
{% load static %}

{% block title %}User Profile - Cloud Operations Central{% endblock %}

{% block extra_css %}
<style>
    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .profile-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin-right: 1.5rem;
    }
    
    .profile-info h2 {
        margin: 0;
        font-weight: 300;
    }
    
    .profile-info p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
    }
    
    .role-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.2);
        font-size: 0.85rem;
        margin-top: 0.5rem;
    }
    
    .stats-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .stats-card h3 {
        color: #667eea;
        margin: 0;
        font-size: 2rem;
        font-weight: 300;
    }
    
    .stats-card p {
        margin: 0.5rem 0 0 0;
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .activity-item {
        border-left: 3px solid #667eea;
        padding: 1rem;
        margin-bottom: 1rem;
        background: #f8f9fa;
        border-radius: 0 8px 8px 0;
    }
    
    .activity-time {
        color: #6c757d;
        font-size: 0.85rem;
    }
    
    .session-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .session-active {
        border-left: 4px solid #28a745;
    }
    
    .permissions-list {
        list-style: none;
        padding: 0;
    }
    
    .permissions-list li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .permissions-list li:last-child {
        border-bottom: none;
    }
    
    .permissions-list i {
        color: #28a745;
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="d-flex align-items-center">
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="profile-info">
                <h2>{{ user.get_full_name|default:user.username }}</h2>
                <p><i class="fas fa-envelope me-2"></i>{{ user.email }}</p>
                {% if user.employee_id %}
                    <p><i class="fas fa-id-badge me-2"></i>Employee ID: {{ user.employee_id }}</p>
                {% endif %}
                <div class="role-badge">
                    <i class="fas fa-shield-alt me-1"></i>{{ user.get_role_display }}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Profile Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>Profile Information</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.email.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                                    {{ form.phone_number }}
                                    {% if form.phone_number.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.phone_number.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Employee ID</label>
                                    <input type="text" class="form-control" value="{{ user.employee_id|default:'Not set' }}" readonly>
                                    <small class="text-muted">Contact administrator to update</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Department</label>
                                    <input type="text" class="form-control" value="{{ user.department|default:'Not set' }}" readonly>
                                    <small class="text-muted">Contact administrator to update</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Business Unit</label>
                                    <input type="text" class="form-control" value="{{ user.business_unit|default:'Not set' }}" readonly>
                                    <small class="text-muted">Contact administrator to update</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Role</label>
                                    <input type="text" class="form-control" value="{{ user.get_role_display }}" readonly>
                                    <small class="text-muted">Contact administrator to change role</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Profile
                            </button>
                            <a href="{% url 'accounts:change_password' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-key me-2"></i>Change Password
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Account Stats -->
            <div class="row">
                <div class="col-6">
                    <div class="stats-card">
                        <h3>{{ user.login_count }}</h3>
                        <p>Total Logins</p>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stats-card">
                        <h3>{{ active_sessions.count }}</h3>
                        <p>Active Sessions</p>
                    </div>
                </div>
            </div>

            <!-- Permissions -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Your Permissions</h6>
                </div>
                <div class="card-body">
                    <ul class="permissions-list">
                        {% for permission in permissions %}
                            <li><i class="fas fa-check"></i>{{ permission }}</li>
                        {% empty %}
                            <li class="text-muted">No specific permissions assigned</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>

            <!-- Account Status -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Account Status</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Account Status:</span>
                        <span class="badge bg-{{ user.is_active|yesno:'success,danger' }}">
                            {{ user.is_active|yesno:'Active,Inactive' }}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Approval Status:</span>
                        <span class="badge bg-{{ user.is_approved|yesno:'success,warning' }}">
                            {{ user.is_approved|yesno:'Approved,Pending' }}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Last Login:</span>
                        <small class="text-muted">
                            {% if user.last_login %}
                                {{ user.last_login|date:"M d, Y H:i" }}
                            {% else %}
                                Never
                            {% endif %}
                        </small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Member Since:</span>
                        <small class="text-muted">{{ user.date_joined|date:"M d, Y" }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h6>
                </div>
                <div class="card-body">
                    {% for activity in recent_activities %}
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>{{ activity.get_action_display }}</strong>
                                    <p class="mb-1">{{ activity.description }}</p>
                                    <div class="activity-time">
                                        <i class="fas fa-clock me-1"></i>{{ activity.timestamp|date:"M d, Y H:i" }}
                                        {% if activity.ip_address %}
                                            <i class="fas fa-map-marker-alt ms-2 me-1"></i>{{ activity.ip_address }}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% empty %}
                        <p class="text-muted">No recent activity</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Active Sessions -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-desktop me-2"></i>Active Sessions</h6>
                </div>
                <div class="card-body">
                    {% for session in active_sessions %}
                        <div class="session-item session-active">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong>
                                        {% if session.session_key == request.session.session_key %}
                                            <i class="fas fa-circle text-success me-1"></i>Current Session
                                        {% else %}
                                            <i class="fas fa-circle text-warning me-1"></i>Other Session
                                        {% endif %}
                                    </strong>
                                    <p class="mb-1 small">{{ session.user_agent|truncatechars:50 }}</p>
                                    <div class="small text-muted">
                                        <i class="fas fa-clock me-1"></i>{{ session.login_time|date:"M d, Y H:i" }}
                                        <i class="fas fa-map-marker-alt ms-2 me-1"></i>{{ session.ip_address }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% empty %}
                        <p class="text-muted">No active sessions</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
