"""
EKS service for AWS inventory operations
Handles EKS clusters, node groups, Fargate profiles, and related operations
"""
import json
import logging
from datetime import datetime
from botocore.exceptions import BotoCoreError, ClientError
from .base import AWSServiceBase


logger = logging.getLogger(__name__)


class EKSService(AWSServiceBase):
    """Service for EKS inventory operations"""
    
    def __init__(self, account_id, region='us-east-1'):
        super().__init__(account_id, region)
        self._eks_client = None
    
    @property
    def eks_client(self):
        """Get EKS client"""
        if not self._eks_client:
            self._eks_client = self.get_client('eks')
        return self._eks_client
    
    def get_clusters(self):
        """Get all EKS clusters for the account"""
        clusters_data = []
        
        try:
            # Get list of cluster names
            cluster_names = self._get_cluster_names()
            
            for cluster_name in cluster_names:
                cluster_data = self._get_cluster_details(cluster_name)
                if cluster_data:
                    clusters_data.append(cluster_data)
        
        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lientError) as e:
            logger.error(f"Error getting EKS clusters for account {self.account_id}: {str(e)}")
            return self.handle_aws_error(e, 'get_clusters')
        
        return clusters_data
    
    def _get_cluster_names(self):
        """Get list of EKS cluster names"""
        try:
            clusters = []
            response = self.eks_client.list_clusters()
            clusters.extend(response.get('clusters', []))

            # Handle pagination
            while 'nextToken' in response:
                response = self.eks_client.list_clusters(nextToken=response['nextToken'])
                clusters.extend(response.get('clusters', []))

            return clusters
        except (BotoCoreError, ClientError) as e:
            logger.error(f"Error listing EKS clusters: {str(e)}")
            return []
    
    def _get_cluster_details(self, cluster_name):
        """Get detailed information about an EKS cluster"""
        try:
            response = self.eks_client.describe_cluster(name=cluster_name)
            cluster = response['cluster']

            # Get addons
            addons = self._get_cluster_addons(cluster_name)
            
            # Get Fargate profiles
            fargate_profiles = self._get_fargate_profile_names(cluster_name)
            
            # Determine cluster type
            cluster_type = self._determine_cluster_type(cluster, fargate_profiles)
            
            # Process network configuration
            vpc_config = cluster.get('resourcesVpcConfig', {})
            
            # Process logging configuration
            logging_config = cluster.get('logging', {})
            logging_enabled = logging_config.get('clusterLogging', [])
            
            # Process encryption configuration
            encryption_config = cluster.get('encryptionConfig', [])
            
            # Process tags
            tags = cluster.get('tags', {})
            flattened_tags = [{'key': k, 'value': v} for k, v in tags.items()]

            return {
                # Core identification
                'cluster_name': cluster_name,
                'arn': cluster.get('arn', ''),
                
                # Versions and status
                'kubernetes_version': cluster.get('version', ''),
                'platform_version': cluster.get('platformVersion', ''),
                'status': cluster.get('status', ''),
                
                # Cluster configuration
                'endpoint': cluster.get('endpoint', ''),
                'cluster_type': cluster_type,
                
                # Network configuration
                'vpc_id': vpc_config.get('vpcId', ''),
                'subnet_ids': json.dumps(vpc_config.get('subnetIds', [])),
                'security_group_ids': json.dumps(vpc_config.get('securityGroupIds', [])),
                'endpoint_public_access': vpc_config.get('endpointPublicAccess', True),
                'endpoint_private_access': vpc_config.get('endpointPrivateAccess', False),
                'public_access_cidrs': json.dumps(vpc_config.get('publicAccessCidrs', [])),
                
                # Service configuration
                'service_ipv4_cidr': cluster.get('kubernetesNetworkConfig', {}).get('serviceIpv4Cidr', ''),
                'service_ipv6_cidr': cluster.get('kubernetesNetworkConfig', {}).get('serviceIpv6Cidr', ''),
                'ip_family': cluster.get('kubernetesNetworkConfig', {}).get('ipFamily', ''),
                
                # Logging configuration
                'logging_enabled': bool(logging_enabled),
                'logging_types': json.dumps([log.get('types', []) for log in logging_enabled]),
                
                # Encryption configuration
                'encryption_enabled': bool(encryption_config),
                'encryption_key_arn': encryption_config[0].get('provider', {}).get('keyArn', '') if encryption_config else '',
                
                # Identity and access
                'role_arn': cluster.get('roleArn', ''),
                'identity_oidc_issuer': cluster.get('identity', {}).get('oidc', {}).get('issuer', ''),
                
                # Addons and features
                'addons': json.dumps(addons),
                'fargate_profile_names': json.dumps(fargate_profiles),
                
                # Timestamps
                'created_at_aws': self.format_datetime(cluster.get('createdAt')),
                
                # Tags
                'all_tags_raw': ', '.join([f"{k}={v}" for k, v in tags.items()]),
                'flattened_tags': flattened_tags,
            }

        except Exception as e:
            logger.error(f"Error getting cluster details for {cluster_name}: {str(e)}")
            return None
    
    def _get_cluster_addons(self, cluster_name):
        """Get list of addons for a cluster"""
        try:
            response = self.eks_client.list_addons(clusterName=cluster_name)
            return response.get('addons', [])
        except Exception as e:
            logger.error(f"Error getting addons for cluster {cluster_name}: {str(e)}")
            return []
    
    def _get_fargate_profile_names(self, cluster_name):
        """Get list of Fargate profile names for a cluster"""
        try:
            response = self.eks_client.list_fargate_profiles(clusterName=cluster_name)
            return response.get('fargateProfileNames', [])
        except Exception as e:
            logger.error(f"Error getting Fargate profiles for cluster {cluster_name}: {str(e)}")
            return []
    
    def _determine_cluster_type(self, cluster, fargate_profiles):
        """Determine cluster type based on configuration"""
        has_fargate = bool(fargate_profiles)
        
        # Check if cluster has node groups (we'll assume it does if no Fargate profiles)
        # In a real implementation, you'd check for node groups
        has_ec2 = True  # Simplified assumption
        
        if has_fargate and has_ec2:
            return 'Hybrid'
        elif has_fargate:
            return 'Fargate'
        else:
            return 'EC2'
    
    def get_node_groups(self, cluster_name):
        """Get node groups for a specific cluster"""
        node_groups_data = []
        
        try:
            # Get list of node group names
            response = self.eks_client.list_nodegroups(clusterName=cluster_name)
            node_group_names = response.get('nodegroups', [])
            
            for node_group_name in node_group_names:
                node_group_data = self._get_node_group_details(cluster_name, node_group_name)
                if node_group_data:
                    node_groups_data.append(node_group_data)
        
        except (BotoCoreError, ClientError) as e:
            logger.error(f"Error getting node groups for cluster {cluster_name}: {str(e)}")
            return self.handle_aws_error(e, 'get_node_groups')
        
        return node_groups_data
    
    def _get_node_group_details(self, cluster_name, node_group_name):
        """Get detailed information about a node group"""
        try:
            response = self.eks_client.describe_nodegroup(
                clusterName=cluster_name,
                nodegroupName=node_group_name
            )
            node_group = response['nodegroup']
            
            # Process scaling configuration
            scaling_config = node_group.get('scalingConfig', {})
            
            # Process launch template
            launch_template = node_group.get('launchTemplate', {})
            
            # Process remote access
            remote_access = node_group.get('remoteAccess', {})
            
            # Process tags
            tags = node_group.get('tags', {})
            flattened_tags = [{'key': k, 'value': v} for k, v in tags.items()]

            return {
                # Core identification
                'node_group_name': node_group_name,
                'arn': node_group.get('arn', ''),
                
                # Status and configuration
                'status': node_group.get('status', ''),
                'capacity_type': node_group.get('capacityType', 'ON_DEMAND'),
                
                # Instance configuration
                'instance_types': json.dumps(node_group.get('instanceTypes', [])),
                'ami_type': node_group.get('amiType', ''),
                'node_role_arn': node_group.get('nodeRole', ''),
                
                # Scaling configuration
                'desired_size': scaling_config.get('desiredSize', 0),
                'min_size': scaling_config.get('minSize', 0),
                'max_size': scaling_config.get('maxSize', 0),
                
                # Network configuration
                'subnet_ids': json.dumps(node_group.get('subnets', [])),
                'remote_access_enabled': bool(remote_access),
                'remote_access_source_sg': json.dumps(remote_access.get('sourceSecurityGroups', [])),
                
                # Launch template
                'launch_template_id': launch_template.get('id', ''),
                'launch_template_version': launch_template.get('version', ''),
                
                # Disk configuration
                'disk_size': node_group.get('diskSize'),
                
                # Timestamps
                'created_at_aws': self.format_datetime(node_group.get('createdAt')),
                'modified_at_aws': self.format_datetime(node_group.get('modifiedAt')),
                
                # Tags
                'all_tags_raw': ', '.join([f"{k}={v}" for k, v in tags.items()]),
                'flattened_tags': flattened_tags,
            }

        except Exception as e:
            logger.error(f"Error getting node group details for {node_group_name}: {str(e)}")
            return None
    
    def get_fargate_profiles(self, cluster_name):
        """Get Fargate profiles for a specific cluster"""
        fargate_profiles_data = []
        
        try:
            # Get list of Fargate profile names
            response = self.eks_client.list_fargate_profiles(clusterName=cluster_name)
            profile_names = response.get('fargateProfileNames', [])
            
            for profile_name in profile_names:
                profile_data = self._get_fargate_profile_details(cluster_name, profile_name)
                if profile_data:
                    fargate_profiles_data.append(profile_data)
        
        except (BotoCoreError, ClientError) as e:
            logger.error(f"Error getting Fargate profiles for cluster {cluster_name}: {str(e)}")
            return self.handle_aws_error(e, 'get_fargate_profiles')
        
        return fargate_profiles_data
    
    def _get_fargate_profile_details(self, cluster_name, profile_name):
        """Get detailed information about a Fargate profile"""
        try:
            response = self.eks_client.describe_fargate_profile(
                clusterName=cluster_name,
                fargateProfileName=profile_name
            )
            profile = response['fargateProfile']
            
            # Process tags
            tags = profile.get('tags', {})
            flattened_tags = [{'key': k, 'value': v} for k, v in tags.items()]

            return {
                # Core identification
                'profile_name': profile_name,
                'arn': profile.get('arn', ''),
                
                # Configuration
                'status': profile.get('status', ''),
                'pod_execution_role_arn': profile.get('podExecutionRoleArn', ''),
                
                # Network configuration
                'subnet_ids': json.dumps(profile.get('subnets', [])),
                
                # Selectors
                'selectors': json.dumps(profile.get('selectors', [])),
                
                # Timestamps
                'created_at_aws': self.format_datetime(profile.get('createdAt')),
                
                # Tags
                'all_tags_raw': ', '.join([f"{k}={v}" for k, v in tags.items()]),
                'flattened_tags': flattened_tags,
            }

        except Exception as e:
            logger.error(f"Error getting Fargate profile details for {profile_name}: {str(e)}")
            return None
