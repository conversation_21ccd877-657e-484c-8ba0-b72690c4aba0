# AWS Boto3 Scripts

This directory contains AWS automation scripts organized by service functionality.

## Directory Structure

### `/ec2/`
Contains scripts related to EC2 instance management and inventory:
- `ec2_inventory.py` - Comprehensive EC2 instance inventory collection

### `/eks/`
Contains scripts related to EKS cluster management and inventory:
- `eks_inventory.py` - EKS cluster, node group, and Fargate profile inventory

### `/ssm/`
Contains scripts related to AWS Systems Manager (SSM):
- `ssm_status_check.py` - SSM agent status monitoring and compliance checking

## Usage

Each script is designed to work with AWS profiles and can be run independently. Make sure you have:

1. AWS CLI configured with appropriate profiles
2. Required Python dependencies installed
3. Proper IAM permissions for the AWS services being accessed

## Configuration

Scripts typically expect:
- AWS profiles named in the format `support-{account_id}`
- A roles configuration file (usually CSV format)
- Appropriate AWS permissions for the target accounts

## Output

Most scripts generate Excel reports with comprehensive data for analysis and reporting purposes.
