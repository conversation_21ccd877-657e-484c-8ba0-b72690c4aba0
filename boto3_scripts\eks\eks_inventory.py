#version 1.0
import boto3
import logging
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side
from datetime import datetime
import time
import sys
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed
import botocore
from botocore.config import Config
from botocore.exceptions import BotoCoreError, ClientError

warnings.filterwarnings('ignore')
print("Script Started...")
now1 = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

# Configure logging
logging.basicConfig(filename=f'aws_eks_inventory-{now1}.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

def get_eks_clusters(eks_client):
    """Get list of EKS clusters in the account"""
    try:
        clusters = []
        response = eks_client.list_clusters()
        clusters.extend(response['clusters'])

        # Handle pagination if needed
        while 'nextToken' in response:
            response = eks_client.list_clusters(nextToken=response['nextToken'])
            clusters.extend(response['clusters'])

        return clusters
    except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ClientError) as e:
        logging.error(f"Error getting EKS clusters: {str(e)}")
        return []

def get_eks_addons(eks_client, cluster_name):
    """Get list of addons for an EKS cluster"""
    try:
        addons = []
        response = eks_client.list_addons(clusterName=cluster_name)
        addons.extend(response.get('addons', []))

        # Handle pagination if needed
        while 'nextToken' in response:
            response = eks_client.list_addons(clusterName=cluster_name, nextToken=response['nextToken'])
            addons.extend(response.get('addons', []))

        return addons
    except (BotoCoreError, ClientError) as e:
        logging.error(f"Error getting EKS addons for cluster {cluster_name}: {str(e)}")
        return []

def get_fargate_profiles(eks_client, cluster_name):
    """Check if the cluster has Fargate profiles"""
    try:
        response = eks_client.list_fargate_profiles(clusterName=cluster_name)
        return response.get('fargateProfileNames', [])
    except (BotoCoreError, ClientError) as e:
        logging.error(f"Error getting Fargate profiles for cluster {cluster_name}: {str(e)}")
        return []

def get_cluster_info(eks_client, cluster_name):
    """Get detailed information about an EKS cluster"""
    try:
        response = eks_client.describe_cluster(name=cluster_name)
        cluster = response['cluster']

        # Get addons
        addons = get_eks_addons(eks_client, cluster_name)
        addons_str = ", ".join(addons) if addons else "None"

        # Check if cluster uses Fargate
        fargate_profiles = get_fargate_profiles(eks_client, cluster_name)
        cluster_type = "Fargate" if fargate_profiles else "EC2"

        # If both EC2 and Fargate are used, mark as hybrid
        if fargate_profiles and cluster.get('resourcesVpcConfig', {}).get('endpointPublicAccess'):
            cluster_type = "Hybrid (EC2 + Fargate)"

        # In EKS, the platform version is the EKS version, while version is the Kubernetes version
        eks_version = cluster.get('platformVersion', 'Unknown')
        kubernetes_version = cluster.get('version', 'Unknown')

        return {
            'Cluster Name': cluster_name,
            'Kubernetes Version': kubernetes_version,
            'EKS Version': eks_version,
            'Status': cluster.get('status', 'Unknown'),
            'Endpoint': cluster.get('endpoint', 'Unknown'),
            'Created At': cluster.get('createdAt', 'Unknown').strftime('%Y-%m-%d %H:%M:%S') if isinstance(cluster.get('createdAt'), datetime) else str(cluster.get('createdAt', 'Unknown')),
            'Addons': addons_str,
            'Cluster Type': cluster_type,
            'ARN': cluster.get('arn', 'Unknown')
        }
    except (BotoCoreError, ClientError) as e:
        logging.error(f"Error getting cluster info for {cluster_name}: {str(e)}")
        return None

def read_roles():
    """Read account information from file"""
    roles = []
    with open('ge_vn_accounts.txt', 'r') as file:
        for line in file.readlines():
            account_id, region, bu = line.strip().split(',')
            roles.append((account_id, region, bu))
    return roles

def save_to_excel(main_data, access_denied_data):
    """Save collected data to Excel file"""
    headers_main = ["BU", "Account Name", "Account ID", "Region", "Cluster Name",
                    "Kubernetes Version", "EKS Version", "Status", "Cluster Type", "Addons",
                    "Created At", "Endpoint", "ARN"]

    df_main = pd.DataFrame(main_data, columns=headers_main)
    df_access_denied = pd.DataFrame(access_denied_data, columns=["Role ARN", "Status", "Reason"])

    now = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_file = f'AWS_EKS_INVENTORY_REPORT_{now}.xlsx'

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df_main.to_excel(writer, index=False, sheet_name='EKS_INVENTORY_AWS')
        df_access_denied.to_excel(writer, index=False, sheet_name='ACCESS_DENIED')

        wb = writer.book
        ws_main = wb['EKS_INVENTORY_AWS']
        ws_access_denied = wb['ACCESS_DENIED']

        # Format headers
        fill = PatternFill(start_color='017054', end_color='017054', fill_type='solid')

        for cell in ws_main[1]:
            cell.fill = fill
            cell.font = Font(bold=True, color='000000')

        # Format cells
        for row in ws_main.iter_rows(min_row=2, max_row=ws_main.max_row, min_col=1, max_col=ws_main.max_column):
            for cell in row:
                cell.border = Border(left=Side(style='thin'),
                                     right=Side(style='thin'),
                                     top=Side(style='thin'),
                                     bottom=Side(style='thin'))

    logging.info(f"Excel file saved: {output_file}")
    print(f"Excel file saved: {output_file}")

def process_role(account_id, role_region, bu):
    """Process each AWS account to collect EKS information"""
    print(f"Switched to account {account_id}, {role_region}, {bu}")
    main_data = []
    access_denied_data = []

    try:
        profile_name = f"support-{account_id}"
        print(f"Using profile: {profile_name}")

        session = boto3.session.Session(profile_name=profile_name, region_name=role_region)

        # Get account alias
        iam_client = session.client('iam', verify=False)
        try:
            account_alias = iam_client.list_account_aliases()['AccountAliases'][0]
        except (KeyError, IndexError):
            account_alias = 'Unknown'

        # Get EKS clusters
        eks_client = session.client('eks', verify=False)
        clusters = get_eks_clusters(eks_client)

        if not clusters:
            print(f"No EKS clusters found in account {account_id}, region {role_region}")
            return main_data, access_denied_data

        for cluster_name in clusters:
            cluster_info = get_cluster_info(eks_client, cluster_name)
            if cluster_info:
                main_data.append([
                    bu,
                    account_alias,
                    account_id,
                    role_region,
                    cluster_info['Cluster Name'],
                    cluster_info['Kubernetes Version'],
                    cluster_info['EKS Version'],
                    cluster_info['Status'],
                    cluster_info['Cluster Type'],
                    cluster_info['Addons'],
                    cluster_info['Created At'],
                    cluster_info['Endpoint'],
                    cluster_info['ARN']
                ])
                print(f"Found EKS cluster: {cluster_name} in account {account_id}, region {role_region}")

    except Exception as e:
        access_denied_data.append([account_id, "Access Denied", str(e)])
        logging.error(f"Error processing role {account_id} in {role_region}: {str(e)}")

    return main_data, access_denied_data

def main():
    """Main function to execute the script"""
    start_time = time.time()
    logging.info(f"Script started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    roles = read_roles()

    main_data = []
    access_denied_data = []

    with ThreadPoolExecutor(max_workers=50) as executor:
        futures = {executor.submit(process_role, account_id, role_region, bu): (account_id, role_region, bu) for account_id, role_region, bu in roles}
        for future in as_completed(futures):
            account_id, role_region, bu = futures[future]
            try:
                data = future.result()
                main_data.extend(data[0])
                access_denied_data.extend(data[1])
            except Exception as e:
                logging.error(f"Error processing role {account_id} in {role_region}: {str(e)}")

    save_to_excel(main_data, access_denied_data)

    end_time = time.time()
    execution_time = end_time - start_time
    logging.info(f"Script ended at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"Total execution time: {execution_time:.2f} seconds")
    print(f"Script execution completed in {execution_time:.2f} seconds")

if __name__ == "__main__":
    main()
