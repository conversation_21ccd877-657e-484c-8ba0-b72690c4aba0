<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Access - Cloud Operations Central</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem 0;
        }
        
        .register-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
        }
        
        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .register-header h2 {
            margin: 0;
            font-weight: 300;
            font-size: 1.8rem;
        }
        
        .register-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .register-body {
            padding: 2rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-floating input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .form-floating input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .login-link {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        .alert {
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        
        .approval-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .approval-info h6 {
            color: #0066cc;
            margin-bottom: 0.5rem;
        }
        
        .approval-info p {
            margin: 0;
            color: #004499;
            font-size: 0.9rem;
        }
        
        .form-row {
            display: flex;
            gap: 1rem;
        }
        
        .form-row .form-floating {
            flex: 1;
        }
        
        .password-requirements {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 0.5rem;
            font-size: 0.8rem;
        }
        
        .password-requirements ul {
            margin: 0;
            padding-left: 1.2rem;
        }
        
        .password-requirements li {
            margin-bottom: 0.2rem;
            color: #6c757d;
        }
        
        .required-field {
            color: #dc3545;
        }
        
        @media (max-width: 576px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .register-container {
                margin: 1rem;
            }
            
            .register-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <i class="fas fa-user-plus fa-2x mb-2"></i>
            <h2>Request Access</h2>
            <p>Cloud Operations Central</p>
        </div>
        
        <div class="register-body">
            <!-- Approval Information -->
            <div class="approval-info">
                <h6><i class="fas fa-info-circle me-1"></i>Account Approval Required</h6>
                <p>Your account will be reviewed by an administrator before access is granted. You'll receive an email notification once approved.</p>
            </div>
            
            <!-- Display messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <!-- Registration Form -->
            <form method="post" id="registrationForm">
                {% csrf_token %}
                
                <!-- Username and Email -->
                <div class="form-row">
                    <div class="form-floating">
                        {{ form.username }}
                        <label for="{{ form.username.id_for_label }}">
                            <i class="fas fa-user me-2"></i>Username <span class="required-field">*</span>
                        </label>
                        {% if form.username.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-floating">
                        {{ form.email }}
                        <label for="{{ form.email.id_for_label }}">
                            <i class="fas fa-envelope me-2"></i>Email Address <span class="required-field">*</span>
                        </label>
                        {% if form.email.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- First and Last Name -->
                <div class="form-row">
                    <div class="form-floating">
                        {{ form.first_name }}
                        <label for="{{ form.first_name.id_for_label }}">
                            <i class="fas fa-user me-2"></i>First Name <span class="required-field">*</span>
                        </label>
                        {% if form.first_name.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.first_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-floating">
                        {{ form.last_name }}
                        <label for="{{ form.last_name.id_for_label }}">
                            <i class="fas fa-user me-2"></i>Last Name <span class="required-field">*</span>
                        </label>
                        {% if form.last_name.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.last_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Employee ID and Phone -->
                <div class="form-row">
                    <div class="form-floating">
                        {{ form.employee_id }}
                        <label for="{{ form.employee_id.id_for_label }}">
                            <i class="fas fa-id-badge me-2"></i>Employee ID
                        </label>
                        {% if form.employee_id.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.employee_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-floating">
                        {{ form.phone_number }}
                        <label for="{{ form.phone_number.id_for_label }}">
                            <i class="fas fa-phone me-2"></i>Phone Number
                        </label>
                        {% if form.phone_number.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.phone_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Department and Business Unit -->
                <div class="form-row">
                    <div class="form-floating">
                        {{ form.department }}
                        <label for="{{ form.department.id_for_label }}">
                            <i class="fas fa-building me-2"></i>Department
                        </label>
                        {% if form.department.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.department.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-floating">
                        {{ form.business_unit }}
                        <label for="{{ form.business_unit.id_for_label }}">
                            <i class="fas fa-sitemap me-2"></i>Business Unit
                        </label>
                        {% if form.business_unit.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.business_unit.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Password Fields -->
                <div class="form-floating">
                    {{ form.password1 }}
                    <label for="{{ form.password1.id_for_label }}">
                        <i class="fas fa-lock me-2"></i>Password <span class="required-field">*</span>
                    </label>
                    {% if form.password1.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.password1.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-floating">
                    {{ form.password2 }}
                    <label for="{{ form.password2.id_for_label }}">
                        <i class="fas fa-lock me-2"></i>Confirm Password <span class="required-field">*</span>
                    </label>
                    {% if form.password2.errors %}
                        <div class="text-danger small mt-1">
                            {% for error in form.password2.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <!-- Password Requirements -->
                <div class="password-requirements">
                    <strong>Password Requirements:</strong>
                    <ul>
                        <li>At least 8 characters long</li>
                        <li>Cannot be too similar to your personal information</li>
                        <li>Cannot be a commonly used password</li>
                        <li>Cannot be entirely numeric</li>
                    </ul>
                </div>
                
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                
                <button type="submit" class="btn btn-primary btn-register">
                    <i class="fas fa-user-plus me-2"></i>Request Access
                </button>
            </form>
            
            <!-- Login Link -->
            <div class="login-link">
                <p class="mb-0">
                    Already have an account? 
                    <a href="{% url 'accounts:login' %}">Sign In</a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('{{ form.username.id_for_label }}');
            if (usernameField) {
                usernameField.focus();
            }
        });
        
        // Form validation feedback
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            const requiredFields = this.querySelectorAll('input[required]');
            let isValid = true;
            
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    </script>
</body>
</html>
