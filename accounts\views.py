from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout, update_session_auth_hash
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponseForbidden
from django.db.models import Q, Count
from django.utils import timezone
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from .models import CustomUser, UserRole, UserSession, UserActivity, SAMLConfiguration
from .forms import (
    CustomUserCreationForm, CustomAuthenticationForm, UserEditForm, 
    UserProfileForm, CustomPasswordChangeForm, UserApprovalForm, BulkUserActionForm
)
from .signals import log_user_activity


def is_admin(user):
    """Check if user is admin"""
    return user.is_authenticated and user.is_admin()


def custom_login_view(request):
    """Custom login view with enhanced security"""
    if request.user.is_authenticated:
        return redirect('inventory:dashboard')
    
    if request.method == 'POST':
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)
            
            # Set session expiry based on remember me
            if not form.cleaned_data.get('remember_me'):
                request.session.set_expiry(0)  # Session expires when browser closes
            else:
                request.session.set_expiry(1209600)  # 2 weeks
            
            messages.success(request, f'Welcome back, {user.get_full_name() or user.username}!')
            
            # Redirect to next page or dashboard
            next_page = request.GET.get('next', 'inventory:dashboard')
            return redirect(next_page)
    else:
        form = CustomAuthenticationForm()
    
    return render(request, 'accounts/login.html', {'form': form})


def custom_logout_view(request):
    """Custom logout view"""
    if request.user.is_authenticated:
        messages.info(request, 'You have been logged out successfully.')
    logout(request)
    return redirect('accounts:login')


def register_view(request):
    """User registration view"""
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(
                request, 
                'Your account has been created successfully! '
                'Please wait for administrator approval before logging in.'
            )
            return redirect('accounts:login')
    else:
        form = CustomUserCreationForm()
    
    return render(request, 'accounts/register.html', {'form': form})


@login_required
def profile_view(request):
    """User profile view"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Your profile has been updated successfully!')
            log_user_activity(request.user, 'profile_update', 'User updated profile', request)
            return redirect('accounts:profile')
    else:
        form = UserProfileForm(instance=request.user)
    
    # Get user's recent activities
    recent_activities = UserActivity.objects.filter(user=request.user)[:10]
    
    # Get user's active sessions
    active_sessions = UserSession.objects.filter(user=request.user, is_active=True)
    
    context = {
        'form': form,
        'recent_activities': recent_activities,
        'active_sessions': active_sessions,
        'permissions': request.user.get_permissions_summary()
    }
    
    return render(request, 'accounts/profile.html', context)


@login_required
def change_password_view(request):
    """Change password view"""
    if request.method == 'POST':
        form = CustomPasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)  # Keep user logged in
            messages.success(request, 'Your password has been changed successfully!')
            log_user_activity(request.user, 'password_change', 'User changed password', request)
            return redirect('accounts:profile')
    else:
        form = CustomPasswordChangeForm(request.user)
    
    return render(request, 'accounts/change_password.html', {'form': form})


class UserManagementMixin(UserPassesTestMixin):
    """Mixin to ensure only admins can access user management"""
    
    def test_func(self):
        return self.request.user.is_authenticated and self.request.user.is_admin()
    
    def handle_no_permission(self):
        messages.error(self.request, 'You do not have permission to access this page.')
        return redirect('inventory:dashboard')


class UserListView(LoginRequiredMixin, UserManagementMixin, ListView):
    """List all users with filtering and search"""
    model = CustomUser
    template_name = 'accounts/user_list.html'
    context_object_name = 'users'
    paginate_by = 25

    def get_queryset(self):
        queryset = CustomUser.objects.select_related('user_role').all()
        
        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search) |
                Q(employee_id__icontains=search) |
                Q(department__icontains=search) |
                Q(business_unit__icontains=search)
            )
        
        # Filter by role
        role = self.request.GET.get('role')
        if role:
            queryset = queryset.filter(user_role__name=role)
        
        # Filter by status
        status = self.request.GET.get('status')
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'pending':
            queryset = queryset.filter(is_approved=False)
        elif status == 'approved':
            queryset = queryset.filter(is_approved=True)
        
        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['roles'] = UserRole.objects.all()
        context['search'] = self.request.GET.get('search', '')
        context['selected_role'] = self.request.GET.get('role', '')
        context['selected_status'] = self.request.GET.get('status', '')
        
        # Statistics
        context['stats'] = {
            'total_users': CustomUser.objects.count(),
            'active_users': CustomUser.objects.filter(is_active=True).count(),
            'pending_approval': CustomUser.objects.filter(is_approved=False).count(),
            'admin_users': CustomUser.objects.filter(user_role__name='admin').count(),
        }
        
        return context


class UserCreateView(LoginRequiredMixin, UserManagementMixin, CreateView):
    """Create new user"""
    model = CustomUser
    form_class = CustomUserCreationForm
    template_name = 'accounts/user_form.html'
    success_url = reverse_lazy('accounts:user_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'User {self.object.username} created successfully!')
        log_user_activity(
            self.request.user, 
            'manage_users', 
            f'Created user: {self.object.username}', 
            self.request
        )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Create User'
        context['roles'] = UserRole.objects.all()
        return context


class UserUpdateView(LoginRequiredMixin, UserManagementMixin, UpdateView):
    """Update user"""
    model = CustomUser
    form_class = UserEditForm
    template_name = 'accounts/user_form.html'
    success_url = reverse_lazy('accounts:user_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request_user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'User {self.object.username} updated successfully!')
        log_user_activity(
            self.request.user, 
            'manage_users', 
            f'Updated user: {self.object.username}', 
            self.request
        )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = f'Edit User: {self.object.username}'
        context['roles'] = UserRole.objects.all()
        return context


@login_required
@user_passes_test(is_admin)
def user_detail_view(request, pk):
    """View user details"""
    user = get_object_or_404(CustomUser, pk=pk)
    
    # Get user's activities
    activities = UserActivity.objects.filter(user=user)[:20]
    
    # Get user's sessions
    sessions = UserSession.objects.filter(user=user)[:10]
    
    context = {
        'user_obj': user,  # Using user_obj to avoid conflict with request.user
        'activities': activities,
        'sessions': sessions,
        'permissions': user.get_permissions_summary()
    }
    
    return render(request, 'accounts/user_detail.html', context)


@login_required
@user_passes_test(is_admin)
@require_http_methods(["POST"])
def approve_user_view(request, pk):
    """Approve user account"""
    user = get_object_or_404(CustomUser, pk=pk)
    
    if not user.is_approved:
        user.approve_user(request.user)
        messages.success(request, f'User {user.username} has been approved!')
        log_user_activity(
            request.user, 
            'manage_users', 
            f'Approved user: {user.username}', 
            request
        )
    else:
        messages.info(request, f'User {user.username} is already approved.')
    
    return redirect('accounts:user_detail', pk=pk)


@login_required
@user_passes_test(is_admin)
@require_http_methods(["POST"])
def toggle_user_status_view(request, pk):
    """Toggle user active status"""
    user = get_object_or_404(CustomUser, pk=pk)
    
    user.is_active = not user.is_active
    user.save()
    
    status = 'activated' if user.is_active else 'deactivated'
    messages.success(request, f'User {user.username} has been {status}!')
    log_user_activity(
        request.user, 
        'manage_users', 
        f'{status.capitalize()} user: {user.username}', 
        request
    )
    
    return redirect('accounts:user_detail', pk=pk)


@login_required
@user_passes_test(is_admin)
def bulk_user_actions_view(request):
    """Handle bulk user actions"""
    if request.method == 'POST':
        form = BulkUserActionForm(request.POST)
        if form.is_valid():
            action = form.cleaned_data['action']
            user_ids = form.cleaned_data['user_ids']
            
            users = CustomUser.objects.filter(id__in=user_ids)
            count = users.count()
            
            if action == 'approve':
                users.filter(is_approved=False).update(
                    is_approved=True,
                    approved_by=request.user,
                    approved_at=timezone.now()
                )
                messages.success(request, f'Approved {count} users.')
            elif action == 'activate':
                users.update(is_active=True)
                messages.success(request, f'Activated {count} users.')
            elif action == 'deactivate':
                users.update(is_active=False)
                messages.success(request, f'Deactivated {count} users.')
            elif action == 'delete':
                # Don't allow deleting superusers or self
                users = users.exclude(is_superuser=True).exclude(id=request.user.id)
                deleted_count = users.count()
                users.delete()
                messages.success(request, f'Deleted {deleted_count} users.')
            
            log_user_activity(
                request.user, 
                'manage_users', 
                f'Bulk action {action} on {count} users', 
                request
            )
    
    return redirect('accounts:user_list')
