from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import CustomUser, UserRole, UserSession, UserActivity, SAMLConfiguration


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'display_name', 'is_active', 'user_count', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'display_name', 'description')
    readonly_fields = ('created_at', 'updated_at')

    def user_count(self, obj):
        """Count users with this role"""
        return obj.users.count()
    user_count.short_description = 'Users'


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = (
        'username', 'email', 'get_full_name', 'user_role', 'business_unit', 
        'is_active', 'is_approved', 'is_saml_user', 'last_login', 'created_at'
    )
    list_filter = (
        'is_active', 'is_approved', 'is_saml_user', 'user_role', 
        'business_unit', 'department', 'created_at', 'last_login'
    )
    search_fields = (
        'username', 'first_name', 'last_name', 'email', 
        'employee_id', 'department', 'business_unit'
    )
    readonly_fields = (
        'created_at', 'updated_at', 'last_login', 'date_joined',
        'login_count', 'failed_login_attempts', 'last_failed_login',
        'saml_user_id', 'saml_attributes'
    )
    
    fieldsets = UserAdmin.fieldsets + (
        ('Additional Information', {
            'fields': (
                'employee_id', 'department', 'business_unit', 
                'manager_email', 'phone_number'
            )
        }),
        ('Role & Permissions', {
            'fields': ('user_role', 'is_approved', 'approved_by', 'approved_at')
        }),
        ('SAML SSO', {
            'fields': ('is_saml_user', 'saml_user_id', 'saml_attributes'),
            'classes': ('collapse',)
        }),
        ('Security & Tracking', {
            'fields': (
                'last_login_ip', 'login_count', 'failed_login_attempts',
                'last_failed_login', 'account_locked_until'
            ),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['approve_users', 'deactivate_users', 'activate_users']

    def approve_users(self, request, queryset):
        """Bulk approve users"""
        count = queryset.filter(is_approved=False).update(
            is_approved=True,
            approved_by=request.user,
            approved_at=timezone.now()
        )
        self.message_user(request, f'Approved {count} users.')
    approve_users.short_description = 'Approve selected users'

    def deactivate_users(self, request, queryset):
        """Bulk deactivate users"""
        count = queryset.filter(is_active=True).update(is_active=False)
        self.message_user(request, f'Deactivated {count} users.')
    deactivate_users.short_description = 'Deactivate selected users'

    def activate_users(self, request, queryset):
        """Bulk activate users"""
        count = queryset.filter(is_active=False).update(is_active=True)
        self.message_user(request, f'Activated {count} users.')
    activate_users.short_description = 'Activate selected users'


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    list_display = (
        'user', 'ip_address', 'login_time', 'last_activity', 
        'is_active', 'session_duration'
    )
    list_filter = ('is_active', 'login_time', 'last_activity')
    search_fields = ('user__username', 'user__email', 'ip_address', 'session_key')
    readonly_fields = ('session_key', 'login_time', 'last_activity', 'logout_time')
    date_hierarchy = 'login_time'

    def session_duration(self, obj):
        """Calculate session duration"""
        if obj.logout_time:
            duration = obj.logout_time - obj.login_time
        elif obj.is_active:
            duration = timezone.now() - obj.login_time
        else:
            duration = obj.last_activity - obj.login_time
        
        total_seconds = int(duration.total_seconds())
        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        if hours > 0:
            return f'{hours}h {minutes}m'
        elif minutes > 0:
            return f'{minutes}m {seconds}s'
        else:
            return f'{seconds}s'
    session_duration.short_description = 'Duration'

    def has_add_permission(self, request):
        """Disable manual session creation"""
        return False


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    list_display = (
        'user', 'action', 'description', 'ip_address', 'timestamp'
    )
    list_filter = ('action', 'timestamp')
    search_fields = ('user__username', 'user__email', 'description', 'ip_address')
    readonly_fields = ('user', 'action', 'description', 'ip_address', 'user_agent', 'additional_data', 'timestamp')
    date_hierarchy = 'timestamp'

    def has_add_permission(self, request):
        """Disable manual activity creation"""
        return False

    def has_change_permission(self, request, obj=None):
        """Disable activity editing"""
        return False


@admin.register(SAMLConfiguration)
class SAMLConfigurationAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'entity_id', 'is_active', 'auto_create_users', 
        'auto_approve_users', 'created_at'
    )
    list_filter = ('is_active', 'auto_create_users', 'auto_approve_users', 'created_at')
    search_fields = ('name', 'entity_id', 'sso_url')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Basic Configuration', {
            'fields': ('name', 'entity_id', 'sso_url', 'slo_url', 'x509_cert')
        }),
        ('Attribute Mapping', {
            'fields': (
                'username_attribute', 'email_attribute', 'first_name_attribute',
                'last_name_attribute', 'employee_id_attribute', 'department_attribute',
                'business_unit_attribute'
            )
        }),
        ('Role Mapping', {
            'fields': (
                'role_attribute', 'admin_role_values', 'automation_user_role_values',
                'reader_role_values'
            )
        }),
        ('Settings', {
            'fields': ('is_active', 'auto_create_users', 'auto_approve_users')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        """Ensure only one active SAML config"""
        if obj.is_active:
            # Deactivate other configurations
            SAMLConfiguration.objects.exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)


# Customize admin site
admin.site.site_header = 'Cloud Operations Central - Administration'
admin.site.site_title = 'Cloud Operations Central Admin'
admin.site.index_title = 'Administration Dashboard'
