#!/usr/bin/env python
"""
Test script to verify EKS implementation
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from django.test import Client
from inventory.models import EKSCluster, EKSNodeGroup, EKSFargateProfile, EKSClusterTag

def print_header(title):
    print("\n" + "="*70)
    print(f" {title}")
    print("="*70)

def print_section(title):
    print(f"\n--- {title} ---")

def test_eks_models():
    print_header("TESTING EKS MODELS")
    
    print_section("Model Creation Test")
    
    try:
        # Test EKSClusterTag model
        tag_count = EKSClusterTag.objects.count()
        print(f"✅ EKSClusterTag model accessible: {tag_count} tags")
        
        # Test EKSCluster model
        cluster_count = EKSCluster.objects.count()
        print(f"✅ EKSCluster model accessible: {cluster_count} clusters")
        
        # Test EKSNodeGroup model
        ng_count = EKSNodeGroup.objects.count()
        print(f"✅ EKSNodeGroup model accessible: {ng_count} node groups")
        
        # Test EKSFargateProfile model
        fp_count = EKSFargateProfile.objects.count()
        print(f"✅ EKSFargateProfile model accessible: {fp_count} Fargate profiles")
        
        return True
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")
        return False

def test_eks_api_endpoints():
    print_header("TESTING EKS API ENDPOINTS")
    
    client = Client()
    
    print_section("API Endpoint Tests")
    
    endpoints = [
        ('/api/eks-clusters/', 'EKS Clusters List'),
        ('/api/eks-clusters/export_excel/', 'EKS Clusters Excel Export'),
    ]
    
    all_passed = True
    
    for endpoint, description in endpoints:
        try:
            response = client.get(endpoint)
            if response.status_code in [200, 302]:  # 302 for redirects
                print(f"✅ {description}: {response.status_code}")
            else:
                print(f"⚠️  {description}: {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
            all_passed = False
    
    return all_passed

def test_eks_template():
    print_header("TESTING EKS TEMPLATE")
    
    client = Client()
    
    print_section("Template Rendering Test")
    
    try:
        response = client.get('/eks-clusters/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Check for key template elements
            has_title = 'EKS Clusters' in content
            has_filters = 'filter-bu' in content and 'filter-account' in content
            has_table = 'clusters-table' in content
            has_modal = 'clusterModal' in content
            has_javascript = 'loadClusters' in content
            has_export = 'exportToExcel' in content
            
            print(f"✅ Template loads successfully")
            print(f"   • Page Title: {'✅' if has_title else '❌'}")
            print(f"   • Filter Controls: {'✅' if has_filters else '❌'}")
            print(f"   • Clusters Table: {'✅' if has_table else '❌'}")
            print(f"   • Detail Modal: {'✅' if has_modal else '❌'}")
            print(f"   • JavaScript Functions: {'✅' if has_javascript else '❌'}")
            print(f"   • Export Functionality: {'✅' if has_export else '❌'}")
            
            return all([has_title, has_filters, has_table, has_modal, has_javascript])
        else:
            print(f"❌ Template failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Template test failed: {e}")
        return False

def test_eks_navigation():
    print_header("TESTING EKS NAVIGATION")
    
    client = Client()
    
    print_section("Navigation Integration Test")
    
    try:
        # Test that EKS link is in the sidebar
        response = client.get('/')  # Dashboard page
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            has_eks_link = '/eks-clusters/' in content
            has_eks_icon = 'fa-cubes' in content
            has_eks_text = 'EKS Clusters' in content
            
            print(f"✅ Dashboard loads successfully")
            print(f"   • EKS Link Present: {'✅' if has_eks_link else '❌'}")
            print(f"   • EKS Icon Present: {'✅' if has_eks_icon else '❌'}")
            print(f"   • EKS Text Present: {'✅' if has_eks_text else '❌'}")
            
            return all([has_eks_link, has_eks_icon, has_eks_text])
        else:
            print(f"❌ Dashboard failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Navigation test failed: {e}")
        return False

def test_eks_serializers():
    print_header("TESTING EKS SERIALIZERS")
    
    print_section("Serializer Import Test")
    
    try:
        from inventory.serializers import (
            EKSClusterListSerializer, 
            EKSClusterDetailedSerializer,
            EKSNodeGroupSerializer,
            EKSFargateProfileSerializer,
            EKSClusterTagSerializer
        )
        
        print("✅ EKSClusterListSerializer imported successfully")
        print("✅ EKSClusterDetailedSerializer imported successfully")
        print("✅ EKSNodeGroupSerializer imported successfully")
        print("✅ EKSFargateProfileSerializer imported successfully")
        print("✅ EKSClusterTagSerializer imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Serializer import failed: {e}")
        return False

def test_eks_management_command():
    print_header("TESTING EKS MANAGEMENT COMMAND")
    
    print_section("Management Command Test")
    
    try:
        from inventory.management.commands.import_eks_clusters import Command
        
        command = Command()
        print("✅ EKS import management command accessible")
        
        # Test help text
        help_text = command.help
        print(f"✅ Command help: {help_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Management command test failed: {e}")
        return False

def test_eks_filters():
    print_header("TESTING EKS FILTERS")
    
    client = Client()
    
    print_section("Filter Parameters Test")
    
    filter_tests = [
        ('?business_unit=Test', 'Business Unit Filter'),
        ('?account_id=************', 'Account ID Filter'),
        ('?status=ACTIVE', 'Status Filter'),
        ('?cluster_type=EC2', 'Cluster Type Filter'),
        ('?kubernetes_version=1.21', 'Kubernetes Version Filter'),
        ('?search=test', 'Search Filter'),
    ]
    
    all_passed = True
    
    for filter_param, description in filter_tests:
        try:
            response = client.get(f'/api/eks-clusters/{filter_param}')
            if response.status_code == 200:
                print(f"✅ {description}: Working")
            else:
                print(f"⚠️  {description}: Status {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
            all_passed = False
    
    return all_passed

def test_eks_dashboard_integration():
    print_header("TESTING EKS DASHBOARD INTEGRATION")
    
    client = Client()
    
    print_section("Dashboard Integration Test")
    
    try:
        response = client.get('/')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Check for EKS table in dashboard
            has_eks_table = 'aws-eks-table' in content
            has_eks_header = 'EKS Clusters' in content
            has_eks_placeholder = 'Coming Soon' in content
            
            print(f"✅ Dashboard loads successfully")
            print(f"   • EKS Table Present: {'✅' if has_eks_table else '❌'}")
            print(f"   • EKS Header Present: {'✅' if has_eks_header else '❌'}")
            print(f"   • EKS Placeholder: {'✅' if has_eks_placeholder else '❌'}")
            
            return True
        else:
            print(f"❌ Dashboard failed to load: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard integration test failed: {e}")
        return False

def main():
    try:
        print_header("EKS IMPLEMENTATION TEST SUITE")
        
        print("🧪 Testing EKS implementation components:")
        print("   • Models and database structure")
        print("   • API endpoints and serializers")
        print("   • Template rendering and JavaScript")
        print("   • Navigation integration")
        print("   • Management commands")
        print("   • Filter functionality")
        print("   • Dashboard integration")
        
        # Run all tests
        models_ok = test_eks_models()
        api_ok = test_eks_api_endpoints()
        template_ok = test_eks_template()
        navigation_ok = test_eks_navigation()
        serializers_ok = test_eks_serializers()
        command_ok = test_eks_management_command()
        filters_ok = test_eks_filters()
        dashboard_ok = test_eks_dashboard_integration()
        
        print_header("EKS IMPLEMENTATION TEST SUMMARY")
        
        if all([models_ok, api_ok, template_ok, navigation_ok, serializers_ok, command_ok, filters_ok, dashboard_ok]):
            print("🎉 SUCCESS: EKS implementation is complete and functional!")
            
            print("\n✅ Implementation Features:")
            print("   • EKS cluster models with full metadata support")
            print("   • Node group and Fargate profile models")
            print("   • Flattened tag storage system")
            print("   • RESTful API with filtering and export")
            print("   • Responsive web interface with detailed views")
            print("   • Cascading filter system (BU → Account)")
            print("   • Excel export functionality")
            print("   • Management command for data import")
            print("   • Dashboard integration")
            
            print("\n📊 EKS Data Structure:")
            print("   • Main Table: Cluster Name, K8s Version, Status, Type, Account, Node Groups, Fargate, Addons, Created")
            print("   • Detailed View: All cluster metadata in flattened format")
            print("   • Related Data: Node groups, Fargate profiles, tags")
            print("   • Filter Options: BU, Account, Status, Type, K8s Version, Search")
            
            print("\n🚀 Ready for Use:")
            print("   1. Run migrations: python manage.py migrate")
            print("   2. Import data: python manage.py import_eks_clusters")
            print("   3. Access EKS page: http://127.0.0.1:8000/eks-clusters/")
            print("   4. View in dashboard: AWS section shows EKS data")
            
        else:
            print("⚠️  WARNING: Some EKS implementation features need attention")
            if not models_ok:
                print("   • Models issues detected")
            if not api_ok:
                print("   • API endpoint issues detected")
            if not template_ok:
                print("   • Template issues detected")
            if not navigation_ok:
                print("   • Navigation issues detected")
            if not serializers_ok:
                print("   • Serializer issues detected")
            if not command_ok:
                print("   • Management command issues detected")
            if not filters_ok:
                print("   • Filter issues detected")
            if not dashboard_ok:
                print("   • Dashboard integration issues detected")
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
