from django.contrib import admin
from django.utils.html import format_html
from django.urls import path
from django.shortcuts import render, redirect
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.utils.safestring import mark_safe
from django.core.exceptions import ValidationError
import csv
import io
from .models import AWSAccount, EC2Instance, SSMStatus, InventoryRefreshLog, InstanceTag


@admin.register(InstanceTag)
class InstanceTagAdmin(admin.ModelAdmin):
    list_display = ['key', 'value', 'instances_count', 'created_at']
    list_filter = ['key']
    search_fields = ['key', 'value']
    ordering = ['key', 'value']

    def instances_count(self, obj):
        return obj.instances.count()
    instances_count.short_description = 'Instances Count'


class BusinessUnitFilter(admin.SimpleListFilter):
    """Custom filter for Business Units with counts"""
    title = 'Business Unit'
    parameter_name = 'business_unit'

    def lookups(self, request, model_admin):
        """Return list of business units with counts"""
        from django.db.models import Count

        # Get business units with account counts
        business_units = AWSAccount.objects.values('business_unit').annotate(
            count=Count('id')
        ).order_by('business_unit')

        lookups = []
        for bu in business_units:
            if bu['business_unit']:
                lookups.append((
                    bu['business_unit'],
                    f"{bu['business_unit']} ({bu['count']})"
                ))

        return lookups

    def queryset(self, request, queryset):
        """Filter queryset based on selected business unit"""
        if self.value():
            return queryset.filter(business_unit=self.value())
        return queryset


class RegionFilter(admin.SimpleListFilter):
    """Custom filter for AWS Regions with counts"""
    title = 'AWS Region'
    parameter_name = 'region'

    def lookups(self, request, model_admin):
        """Return list of regions with counts"""
        from django.db.models import Count

        # Get regions with account counts
        regions = AWSAccount.objects.values('region').annotate(
            count=Count('id')
        ).order_by('region')

        lookups = []
        for region in regions:
            if region['region']:
                lookups.append((
                    region['region'],
                    f"{region['region']} ({region['count']})"
                ))

        return lookups

    def queryset(self, request, queryset):
        """Filter queryset based on selected region"""
        if self.value():
            return queryset.filter(region=self.value())
        return queryset


@admin.register(AWSAccount)
class AWSAccountAdmin(admin.ModelAdmin):
    list_display = ['account_name_display', 'account_id', 'business_unit_display', 'region_display', 'status_display', 'created_at']
    list_filter = [BusinessUnitFilter, RegionFilter, 'is_active', 'created_at']
    search_fields = ['account_name', 'account_id', 'business_unit', 'region']
    ordering = ['business_unit', 'account_name']
    actions = ['export_selected_accounts', 'activate_accounts', 'deactivate_accounts']
    list_per_page = 25
    date_hierarchy = 'created_at'

    def account_name_display(self, obj):
        """Enhanced account name display with icon"""
        if obj.is_active:
            icon = '<i class="fas fa-check-circle" style="color: green;" title="Active"></i>'
        else:
            icon = '<i class="fas fa-times-circle" style="color: red;" title="Inactive"></i>'
        return format_html(f'{icon} <strong>{obj.account_name}</strong>')
    account_name_display.short_description = 'Account Name'
    account_name_display.admin_order_field = 'account_name'

    def business_unit_display(self, obj):
        """Enhanced business unit display with badge"""
        if obj.business_unit:
            # Color coding for different business units
            colors = {
                'HQ CTO Vernova': '#007cba',
                'Gas Power': '#28a745',
                'Renewable Energy': '#17a2b8',
                'Wind Power': '#6f42c1',
                'Hydro Power': '#20c997',
                'Nuclear Power': '#fd7e14',
                'Solar Energy': '#ffc107',
                'Grid Solutions': '#dc3545',
                'Digital Services': '#6c757d',
                'Energy Transition': '#e83e8c'
            }
            color = colors.get(obj.business_unit, '#6c757d')
            return format_html(
                '<span style="background-color: {}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">{}</span>',
                color, obj.business_unit
            )
        return '-'
    business_unit_display.short_description = 'Business Unit'
    business_unit_display.admin_order_field = 'business_unit'

    def region_display(self, obj):
        """Enhanced region display with flag icons"""
        region_flags = {
            'us-east-1': '🇺🇸 N. Virginia',
            'us-east-2': '🇺🇸 Ohio',
            'us-west-1': '🇺🇸 N. California',
            'us-west-2': '🇺🇸 Oregon',
            'eu-west-1': '🇮🇪 Ireland',
            'eu-west-2': '🇬🇧 London',
            'eu-west-3': '🇫🇷 Paris',
            'eu-central-1': '🇩🇪 Frankfurt',
            'ap-southeast-1': '🇸🇬 Singapore',
            'ap-southeast-2': '🇦🇺 Sydney',
            'ap-northeast-1': '🇯🇵 Tokyo',
            'ap-northeast-2': '🇰🇷 Seoul',
            'ca-central-1': '🇨🇦 Canada',
            'sa-east-1': '🇧🇷 São Paulo'
        }
        display_name = region_flags.get(obj.region, f'🌍 {obj.region}')
        return format_html('<span title="{}">{}</span>', obj.region, display_name)
    region_display.short_description = 'Region'
    region_display.admin_order_field = 'region'

    def status_display(self, obj):
        """Enhanced status display"""
        if obj.is_active:
            return format_html('<span style="color: green; font-weight: bold;">✓ Active</span>')
        else:
            return format_html('<span style="color: red; font-weight: bold;">✗ Inactive</span>')
    status_display.short_description = 'Status'
    status_display.admin_order_field = 'is_active'

    def get_urls(self):
        """Add custom URLs for bulk upload"""
        urls = super().get_urls()
        custom_urls = [
            path('bulk-upload/', self.admin_site.admin_view(self.bulk_upload_view), name='inventory_awsaccount_bulk_upload'),
        ]
        return custom_urls + urls

    def changelist_view(self, request, extra_context=None):
        """Enhanced changelist view with statistics and bulk upload"""
        extra_context = extra_context or {}
        extra_context['bulk_upload_url'] = 'bulk-upload/'

        # Add statistics
        from django.db.models import Count, Q

        total_accounts = AWSAccount.objects.count()
        active_accounts = AWSAccount.objects.filter(is_active=True).count()
        inactive_accounts = total_accounts - active_accounts

        # Business unit statistics
        bu_stats = AWSAccount.objects.values('business_unit').annotate(
            count=Count('id')
        ).order_by('-count')

        # Region statistics
        region_stats = AWSAccount.objects.values('region').annotate(
            count=Count('id')
        ).order_by('-count')

        extra_context.update({
            'total_accounts': total_accounts,
            'active_accounts': active_accounts,
            'inactive_accounts': inactive_accounts,
            'bu_stats': bu_stats[:5],  # Top 5 business units
            'region_stats': region_stats[:5],  # Top 5 regions
        })

        return super().changelist_view(request, extra_context)

    def activate_accounts(self, request, queryset):
        """Activate selected accounts"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'Successfully activated {updated} accounts.')
    activate_accounts.short_description = "Activate selected accounts"

    def deactivate_accounts(self, request, queryset):
        """Deactivate selected accounts"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'Successfully deactivated {updated} accounts.')
    deactivate_accounts.short_description = "Deactivate selected accounts"

    def bulk_upload_view(self, request):
        """Handle bulk upload of AWS accounts from CSV"""
        if request.method == 'POST':
            csv_file = request.FILES.get('csv_file')

            if not csv_file:
                messages.error(request, 'Please select a CSV file to upload.')
                return render(request, 'admin/inventory/awsaccount/bulk_upload.html')

            if not csv_file.name.endswith('.csv'):
                messages.error(request, 'Please upload a CSV file.')
                return render(request, 'admin/inventory/awsaccount/bulk_upload.html')

            try:
                # Read and process CSV file
                decoded_file = csv_file.read().decode('utf-8')
                csv_data = csv.DictReader(io.StringIO(decoded_file))

                created_count = 0
                updated_count = 0
                error_count = 0
                errors = []

                for row_num, row in enumerate(csv_data, start=2):  # Start at 2 because row 1 is header
                    try:
                        # Clean and validate data
                        account_id = str(row.get('account_id', '')).strip()
                        account_name = str(row.get('account_name', '')).strip()
                        region = str(row.get('region', '')).strip()
                        business_unit = str(row.get('bu', '')).strip()

                        # Validate required fields
                        if not account_id:
                            errors.append(f"Row {row_num}: Account ID is required")
                            error_count += 1
                            continue

                        if not account_name:
                            errors.append(f"Row {row_num}: Account name is required")
                            error_count += 1
                            continue

                        if not region:
                            errors.append(f"Row {row_num}: Region is required")
                            error_count += 1
                            continue

                        if not business_unit:
                            errors.append(f"Row {row_num}: Business unit is required")
                            error_count += 1
                            continue

                        # Validate account ID format (12 digits)
                        if not account_id.isdigit() or len(account_id) != 12:
                            errors.append(f"Row {row_num}: Account ID must be 12 digits")
                            error_count += 1
                            continue

                        # Create or update account
                        account, created = AWSAccount.objects.update_or_create(
                            account_id=account_id,
                            defaults={
                                'account_name': account_name,
                                'region': region,
                                'business_unit': business_unit,
                                'is_active': True
                            }
                        )

                        if created:
                            created_count += 1
                        else:
                            updated_count += 1

                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")
                        error_count += 1

                # Show results
                if created_count > 0:
                    messages.success(request, f'Successfully created {created_count} new AWS accounts.')

                if updated_count > 0:
                    messages.info(request, f'Updated {updated_count} existing AWS accounts.')

                if error_count > 0:
                    error_message = f'Failed to process {error_count} rows:\n' + '\n'.join(errors[:10])
                    if len(errors) > 10:
                        error_message += f'\n... and {len(errors) - 10} more errors.'
                    messages.error(request, error_message)

                if created_count > 0 or updated_count > 0:
                    return HttpResponseRedirect('../')

            except Exception as e:
                messages.error(request, f'Error processing CSV file: {str(e)}')

        return render(request, 'admin/inventory/awsaccount/bulk_upload.html')

    def export_selected_accounts(self, request, queryset):
        """Export selected accounts to CSV"""
        import csv
        from django.http import HttpResponse

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="aws_accounts.csv"'

        writer = csv.writer(response)
        writer.writerow(['account_id', 'account_name', 'region', 'bu'])

        for account in queryset:
            writer.writerow([
                account.account_id,
                account.account_name,
                account.region,
                account.business_unit
            ])

        return response

    export_selected_accounts.short_description = "Export selected accounts to CSV"


@admin.register(EC2Instance)
class EC2InstanceAdmin(admin.ModelAdmin):
    list_display = ['instance_id', 'name', 'instance_type', 'state', 'account', 'env_tag', 'uai_tag', 'cto_cloud_ops_managed', 'last_updated']
    list_filter = ['state', 'instance_type', 'account__business_unit', 'env_tag', 'uai_tag', 'cto_cloud_ops_managed']
    search_fields = ['instance_id', 'name', 'private_ip_address', 'ami_id', 'subnet_id']
    ordering = ['-last_updated']
    readonly_fields = ['created_at', 'last_updated']
    filter_horizontal = ['tags']

    fieldsets = (
        ('Basic Information', {
            'fields': ('account', 'instance_id', 'name', 'state', 'instance_type')
        }),
        ('Specifications', {
            'fields': ('vcpu_count', 'memory_gb', 'ami_id', 'os_information', 'platform_type')
        }),
        ('Network & Security', {
            'fields': ('private_ip_address', 'private_dns_name', 'subnet_id', 'subnet_name', 'security_groups', 'instance_profile')
        }),
        ('Storage', {
            'fields': ('root_volume_id', 'root_volume_size_gb', 'data_disk_count')
        }),
        ('Tags', {
            'fields': ('env_tag', 'uai_tag', 'patch_tag', 'app_env_cfg_id', 'maintenance_schedule',
                      'schedule_tag', 'backup_tag', 'cto_cloud_ops_managed', 'patch_provider',
                      'patch_group', 'patch_exempt')
        }),
        ('Flattened Tags', {
            'fields': ('tags', 'all_tags_raw'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'last_updated'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SSMStatus)
class SSMStatusAdmin(admin.ModelAdmin):
    list_display = ['instance', 'ping_status', 'agent_version', 'is_latest_version', 'last_ping_datetime', 'last_checked']
    list_filter = ['ping_status', 'is_latest_version', 'ssm_configured', 'platform_type']
    search_fields = ['instance__instance_id', 'instance__name', 'agent_version']
    ordering = ['-last_checked']
    readonly_fields = ['last_checked', 'created_at']


@admin.register(InventoryRefreshLog)
class InventoryRefreshLogAdmin(admin.ModelAdmin):
    list_display = ['account', 'status', 'started_at', 'completed_at', 'instances_processed', 'errors_count']
    list_filter = ['status', 'account__business_unit']
    search_fields = ['account__account_name', 'account__account_id']
    ordering = ['-started_at']
    readonly_fields = ['started_at', 'completed_at']
