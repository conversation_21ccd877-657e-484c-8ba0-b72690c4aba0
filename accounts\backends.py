from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import User<PERSON>ole, SAMLConfiguration
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class CustomAuthenticationBackend(ModelBackend):
    """Custom authentication backend with account locking and role validation"""
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """Authenticate user with additional security checks"""
        if username is None or password is None:
            return None
        
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user
            User().set_password(password)
            return None
        
        # Check if account is locked (only if custom user model)
        if hasattr(user, 'is_account_locked') and user.is_account_locked():
            logger.warning(f"Login attempt for locked account: {username}")
            return None

        # Check if account is approved (only if custom user model)
        if hasattr(user, 'is_approved') and not user.is_approved and not user.is_superuser:
            logger.warning(f"Login attempt for unapproved account: {username}")
            return None
        
        # Check password and return user if valid
        if user.check_password(password) and self.user_can_authenticate(user):
            return user
        
        return None
    
    def user_can_authenticate(self, user):
        """Check if user can authenticate"""
        is_active = getattr(user, 'is_active', None)
        return is_active or is_active is None


class SAMLAuthenticationBackend:
    """SAML authentication backend for future SSO implementation"""
    
    def authenticate(self, request, saml_user_id=None, saml_attributes=None, **kwargs):
        """Authenticate user via SAML SSO"""
        if not saml_user_id or not saml_attributes:
            return None
        
        try:
            # Get active SAML configuration
            saml_config = SAMLConfiguration.objects.filter(is_active=True).first()
            if not saml_config:
                logger.error("No active SAML configuration found")
                return None
            
            # Try to find existing SAML user
            try:
                user = User.objects.get(saml_user_id=saml_user_id)
                # Update user attributes from SAML
                self._update_user_from_saml(user, saml_attributes, saml_config)
                return user
            except User.DoesNotExist:
                # Create new user if auto-creation is enabled
                if saml_config.auto_create_users:
                    user = self._create_user_from_saml(saml_user_id, saml_attributes, saml_config)
                    return user
                else:
                    logger.warning(f"SAML user {saml_user_id} not found and auto-creation disabled")
                    return None
        
        except Exception as e:
            logger.error(f"SAML authentication error: {str(e)}")
            return None
    
    def get_user(self, user_id):
        """Get user by ID"""
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
    
    def _create_user_from_saml(self, saml_user_id, saml_attributes, saml_config):
        """Create new user from SAML attributes"""
        try:
            # Extract user information from SAML attributes
            username = saml_attributes.get(saml_config.username_attribute, saml_user_id)
            email = saml_attributes.get(saml_config.email_attribute, '')
            first_name = saml_attributes.get(saml_config.first_name_attribute, '')
            last_name = saml_attributes.get(saml_config.last_name_attribute, '')
            employee_id = saml_attributes.get(saml_config.employee_id_attribute, '')
            department = saml_attributes.get(saml_config.department_attribute, '')
            business_unit = saml_attributes.get(saml_config.business_unit_attribute, '')
            
            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name,
                employee_id=employee_id,
                department=department,
                business_unit=business_unit,
                saml_user_id=saml_user_id,
                is_saml_user=True,
                saml_attributes=saml_attributes,
                is_approved=saml_config.auto_approve_users
            )
            
            # Assign role based on SAML attributes
            self._assign_role_from_saml(user, saml_attributes, saml_config)
            
            logger.info(f"Created new SAML user: {username}")
            return user
        
        except Exception as e:
            logger.error(f"Error creating SAML user: {str(e)}")
            return None
    
    def _update_user_from_saml(self, user, saml_attributes, saml_config):
        """Update existing user with SAML attributes"""
        try:
            # Update user information
            user.email = saml_attributes.get(saml_config.email_attribute, user.email)
            user.first_name = saml_attributes.get(saml_config.first_name_attribute, user.first_name)
            user.last_name = saml_attributes.get(saml_config.last_name_attribute, user.last_name)
            user.employee_id = saml_attributes.get(saml_config.employee_id_attribute, user.employee_id)
            user.department = saml_attributes.get(saml_config.department_attribute, user.department)
            user.business_unit = saml_attributes.get(saml_config.business_unit_attribute, user.business_unit)
            user.saml_attributes = saml_attributes
            
            # Update role if needed
            self._assign_role_from_saml(user, saml_attributes, saml_config)
            
            user.save()
            logger.info(f"Updated SAML user: {user.username}")
        
        except Exception as e:
            logger.error(f"Error updating SAML user: {str(e)}")
    
    def _assign_role_from_saml(self, user, saml_attributes, saml_config):
        """Assign user role based on SAML attributes"""
        try:
            role_value = saml_attributes.get(saml_config.role_attribute, '')
            
            # Determine role based on SAML role values
            if role_value in saml_config.admin_role_values:
                role_name = 'admin'
            elif role_value in saml_config.automation_user_role_values:
                role_name = 'automation_user'
            elif role_value in saml_config.reader_role_values:
                role_name = 'reader'
            else:
                role_name = 'reader'  # Default role
            
            # Assign role
            try:
                role = UserRole.objects.get(name=role_name)
                user.user_role = role
                user.save()
                logger.info(f"Assigned role {role_name} to SAML user {user.username}")
            except UserRole.DoesNotExist:
                logger.error(f"Role {role_name} not found for SAML user {user.username}")
        
        except Exception as e:
            logger.error(f"Error assigning role to SAML user: {str(e)}")


class EmailAuthenticationBackend(ModelBackend):
    """Allow authentication using email address"""
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """Authenticate using email as username"""
        if '@' in username:
            try:
                user = User.objects.get(email=username)
                if user.check_password(password) and self.user_can_authenticate(user):
                    # Check additional security constraints (only if custom user model)
                    if hasattr(user, 'is_account_locked') and user.is_account_locked():
                        return None
                    if hasattr(user, 'is_approved') and not user.is_approved and not user.is_superuser:
                        return None
                    return user
            except User.DoesNotExist:
                return None
        return None
