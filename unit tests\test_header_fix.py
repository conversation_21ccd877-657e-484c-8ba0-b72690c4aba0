#!/usr/bin/env python
"""
Test script to verify AWS/Azure headers are visible and page header is hidden
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from django.test import Client

def print_header(title):
    print("\n" + "="*50)
    print(f" {title}")
    print("="*50)

def test_header_visibility():
    print_header("TESTING HEADER VISIBILITY FIX")
    
    client = Client()
    
    response = client.get('/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check for AWS and Azure headers
        has_aws_header = 'aws-header' in content
        has_azure_header = 'azure-header' in content
        has_section_header_class = 'section-header' in content
        
        # Check for specific CSS targeting
        has_specific_css = '.main-content .page-header' in content
        has_general_css = '.page-header {' in content and '.main-content .page-header' not in content
        
        # Check for AWS and Azure content
        has_aws_content = 'fab fa-aws' in content
        has_azure_content = 'fab fa-microsoft' in content
        
        print("✅ Dashboard loads successfully")
        print(f"\n🎨 Section Headers:")
        print(f"   • AWS Header Class: {'✅' if has_aws_header else '❌'}")
        print(f"   • Azure Header Class: {'✅' if has_azure_header else '❌'}")
        print(f"   • Section Header Class: {'✅' if has_section_header_class else '❌'}")
        
        print(f"\n☁️ Cloud Provider Content:")
        print(f"   • AWS Icon: {'✅' if has_aws_content else '❌'}")
        print(f"   • Azure Icon: {'✅' if has_azure_content else '❌'}")
        
        print(f"\n📜 CSS Targeting:")
        print(f"   • Specific CSS (.main-content .page-header): {'✅' if has_specific_css else '❌'}")
        print(f"   • General CSS (.page-header only): {'❌' if not has_general_css else '⚠️ Too broad'}")
        
        if has_aws_header and has_azure_header and has_specific_css:
            print(f"\n🎉 SUCCESS: AWS and Azure headers are visible!")
            print(f"   • Page header is specifically hidden")
            print(f"   • Section headers remain visible")
            return True
        else:
            print(f"\n⚠️ Issues detected:")
            if not has_aws_header:
                print(f"   • AWS header missing")
            if not has_azure_header:
                print(f"   • Azure header missing")
            if not has_specific_css:
                print(f"   • CSS not specific enough")
            return False
    else:
        print(f"❌ Dashboard failed to load: {response.status_code}")
        return False

def main():
    try:
        print_header("AWS/AZURE HEADER VISIBILITY TEST")
        
        success = test_header_visibility()
        
        if success:
            print(f"\n✅ FIXED: AWS and Azure headers are now visible")
            print(f"   • Changed CSS from '.page-header' to '.main-content .page-header'")
            print(f"   • This specifically targets only the page header div")
            print(f"   • Section headers (aws-header, azure-header) remain visible")
            
            print(f"\n🎯 CSS Change:")
            print(f"   Before: .page-header {{ display: none !important; }}")
            print(f"   After:  .main-content .page-header {{ display: none !important; }}")
            
            print(f"\n🚀 Ready to use:")
            print(f"   1. Start server: python manage.py runserver")
            print(f"   2. Access: http://127.0.0.1:8000")
            print(f"   3. AWS and Azure headers should be visible")
            print(f"   4. Page header should still be hidden")
        else:
            print(f"\n❌ Issues still exist - may need further investigation")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
