#!/usr/bin/env python
"""
Test script to verify AWS header visibility and popup notifications
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from django.test import Client

def print_header(title):
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_section(title):
    print(f"\n--- {title} ---")

def test_header_positioning():
    print_header("TESTING AWS HEADER POSITIONING")
    
    client = Client()
    
    response = client.get('/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check for proper padding and positioning
        has_top_padding = 'padding: 12px 8px 8px 8px' in content
        has_dashboard_padding = 'padding-top: 8px' in content and 'dashboard-container' in content
        has_page_header_hidden = '.main-content .page-header' in content and 'display: none' in content
        
        # Check for AWS and Azure headers
        has_aws_header = 'aws-header' in content
        has_azure_header = 'azure-header' in content
        has_section_headers = 'section-header' in content
        
        print("✅ Dashboard loads successfully")
        print(f"\n📐 Positioning Fixes:")
        print(f"   • Main Content Top Padding: {'✅' if has_top_padding else '❌'}")
        print(f"   • Dashboard Container Padding: {'✅' if has_dashboard_padding else '❌'}")
        print(f"   • Page Header Hidden: {'✅' if has_page_header_hidden else '❌'}")
        
        print(f"\n🎨 Section Headers:")
        print(f"   • AWS Header Class: {'✅' if has_aws_header else '❌'}")
        print(f"   • Azure Header Class: {'✅' if has_azure_header else '❌'}")
        print(f"   • Section Header Class: {'✅' if has_section_headers else '❌'}")
        
        return all([has_top_padding, has_dashboard_padding, has_aws_header, has_azure_header])
    else:
        print(f"❌ Dashboard failed to load: {response.status_code}")
        return False

def test_popup_notifications():
    print_header("TESTING POPUP NOTIFICATIONS")
    
    client = Client()
    
    response = client.get('/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check for popup notification CSS
        has_popup_css = 'notification-popup' in content
        has_fixed_position = 'position: fixed' in content and 'notification-popup' in content
        has_z_index = 'z-index: 9999' in content
        has_transform_animation = 'transform: translateX' in content
        has_transition = 'transition: transform 0.3s ease-in-out' in content
        
        # Check for different alert types
        has_success_style = 'alert-success' in content
        has_danger_style = 'alert-danger' in content
        has_warning_style = 'alert-warning' in content
        has_info_style = 'alert-info' in content
        
        # Check for JavaScript functions
        has_popup_function = 'showPopupNotification' in content
        has_show_alert_override = 'window.showAlert' in content
        has_close_button = 'close-btn' in content
        
        print("✅ Dashboard loads successfully")
        print(f"\n🎨 Popup CSS:")
        print(f"   • Popup Class: {'✅' if has_popup_css else '❌'}")
        print(f"   • Fixed Position: {'✅' if has_fixed_position else '❌'}")
        print(f"   • High Z-Index: {'✅' if has_z_index else '❌'}")
        print(f"   • Transform Animation: {'✅' if has_transform_animation else '❌'}")
        print(f"   • Smooth Transition: {'✅' if has_transition else '❌'}")
        
        print(f"\n🎯 Alert Types:")
        print(f"   • Success Style: {'✅' if has_success_style else '❌'}")
        print(f"   • Danger Style: {'✅' if has_danger_style else '❌'}")
        print(f"   • Warning Style: {'✅' if has_warning_style else '❌'}")
        print(f"   • Info Style: {'✅' if has_info_style else '❌'}")
        
        print(f"\n📜 JavaScript Functions:")
        print(f"   • Popup Function: {'✅' if has_popup_function else '❌'}")
        print(f"   • ShowAlert Override: {'✅' if has_show_alert_override else '❌'}")
        print(f"   • Close Button: {'✅' if has_close_button else '❌'}")
        
        return all([has_popup_css, has_fixed_position, has_popup_function, has_show_alert_override])
    else:
        print(f"❌ Dashboard failed to load: {response.status_code}")
        return False

def test_notification_integration():
    print_header("TESTING NOTIFICATION INTEGRATION")
    
    client = Client()
    
    response = client.get('/')
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        
        # Check for success notifications in AJAX calls
        has_success_notifications = 'showPopupNotification' in content and 'success' in content
        has_error_notifications = 'showPopupNotification' in content and 'danger' in content
        
        # Check for specific success messages
        has_accounts_success = 'AWS accounts data loaded successfully' in content
        has_ec2_success = 'AWS EC2 data loaded successfully' in content
        
        # Check for specific error messages
        has_accounts_error = 'Failed to load AWS accounts data' in content
        has_ec2_error = 'Failed to load AWS EC2 data' in content
        
        print("✅ Dashboard loads successfully")
        print(f"\n📡 AJAX Integration:")
        print(f"   • Success Notifications: {'✅' if has_success_notifications else '❌'}")
        print(f"   • Error Notifications: {'✅' if has_error_notifications else '❌'}")
        
        print(f"\n✅ Success Messages:")
        print(f"   • Accounts Success: {'✅' if has_accounts_success else '❌'}")
        print(f"   • EC2 Success: {'✅' if has_ec2_success else '❌'}")
        
        print(f"\n❌ Error Messages:")
        print(f"   • Accounts Error: {'✅' if has_accounts_error else '❌'}")
        print(f"   • EC2 Error: {'✅' if has_ec2_error else '❌'}")
        
        return all([has_success_notifications, has_error_notifications, has_accounts_success, has_ec2_success])
    else:
        print(f"❌ Dashboard failed to load: {response.status_code}")
        return False

def main():
    try:
        print_header("HEADER VISIBILITY & POPUP NOTIFICATIONS TEST")
        
        print("🔧 Testing fixes for:")
        print("   • AWS header visibility after page header removal")
        print("   • Popup-style notifications instead of inline alerts")
        print("   • Proper spacing and positioning")
        print("   • Smooth animations and user experience")
        
        # Run tests
        positioning_ok = test_header_positioning()
        popups_ok = test_popup_notifications()
        integration_ok = test_notification_integration()
        
        print_header("TEST SUMMARY")
        
        if positioning_ok and popups_ok and integration_ok:
            print("🎉 SUCCESS: All fixes implemented successfully!")
            
            print("\n✅ Header Positioning Fixed:")
            print("   • Main content top padding: 12px (instead of 8px)")
            print("   • Dashboard container padding: 8px additional")
            print("   • AWS header now visible and properly positioned")
            print("   • Azure header also visible and positioned correctly")
            
            print("\n🎯 Popup Notifications Implemented:")
            print("   • Fixed position: top-right corner")
            print("   • Smooth slide-in animation from right")
            print("   • Auto-hide after 4 seconds")
            print("   • Manual close button available")
            print("   • Different styles for success/error/warning/info")
            
            print("\n📡 AJAX Integration:")
            print("   • Success notifications for data loading")
            print("   • Error notifications for failed requests")
            print("   • Replaced inline alerts with popups")
            print("   • Better user experience")
            
            print("\n🎨 Visual Improvements:")
            print("   • No more inline alert banners")
            print("   • Clean popup notifications")
            print("   • Proper AWS/Azure header visibility")
            print("   • Professional notification styling")
            
        else:
            print("⚠️  WARNING: Some features may need attention")
            if not positioning_ok:
                print("   • Header positioning issues detected")
            if not popups_ok:
                print("   • Popup notification issues detected")
            if not integration_ok:
                print("   • AJAX integration issues detected")
        
        print("\n🚀 Ready for Use:")
        print("   1. Start server: python manage.py runserver")
        print("   2. Access dashboard: http://127.0.0.1:8000")
        print("   3. AWS and Azure headers should be visible")
        print("   4. Notifications appear as popups in top-right")
        print("   5. Clean, professional dashboard experience")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
