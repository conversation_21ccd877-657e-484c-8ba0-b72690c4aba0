"""
Base models for AWS inventory system
Contains core models like AWSAccount and shared utilities
"""
from django.db import models
from django.utils import timezone


class AWSAccount(models.Model):
    """Model to store AWS account information"""
    account_id = models.CharField(max_length=12, unique=True)
    account_name = models.CharField(max_length=255, blank=True)
    region = models.CharField(max_length=50)
    business_unit = models.CharField(max_length=100)  # BU
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['business_unit', 'account_name']

    def __str__(self):
        return f"{self.account_name} ({self.account_id}) - {self.business_unit}"


class InventoryRefreshLog(models.Model):
    """Model to track inventory refresh operations"""
    STATUS_CHOICES = [
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE, related_name='refresh_logs')
    status = models.Char<PERSON>ield(max_length=20, choices=STATUS_CHOICES, default='running')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    instances_processed = models.IntegerField(default=0)
    errors_count = models.IntegerField(default=0)
    error_details = models.TextField(blank=True)

    class Meta:
        ordering = ['-started_at']

    def __str__(self):
        return f"Refresh {self.account.account_name} - {self.status} ({self.started_at})"
