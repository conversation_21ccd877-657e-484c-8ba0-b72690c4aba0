#!/usr/bin/env python
"""
Create sample EKS data for demonstration
"""
import os
import sys
import django
import json
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'aws_inventory.settings')
django.setup()

from django.utils import timezone
from inventory.models import AWSAccount, EKSCluster, EKSNodeGroup, EKSFargateProfile, EKSClusterTag

def create_sample_eks_data():
    print("Creating sample EKS data...")
    
    # Get existing accounts
    accounts = list(AWSAccount.objects.all())
    if not accounts:
        print("❌ No AWS accounts found. Please create accounts first.")
        return
    
    # Create sample EKS clusters
    sample_clusters = [
        {
            'account': accounts[0],  # Gas Power account
            'cluster_name': 'gp-prod-eks-cluster',
            'kubernetes_version': '1.28',
            'platform_version': 'eks.5',
            'status': 'ACTIVE',
            'cluster_type': 'Hybrid',
            'endpoint': 'https://A1B2C3D4E5F6G7H8I9J0.gr7.us-east-1.eks.amazonaws.com',
            'vpc_id': 'vpc-0123456789abcdef0',
            'subnet_ids': json.dumps(['subnet-0123456789abcdef0', 'subnet-0123456789abcdef1']),
            'endpoint_public_access': True,
            'endpoint_private_access': True,
            'logging_enabled': True,
            'logging_types': json.dumps(['api', 'audit', 'authenticator']),
            'encryption_enabled': True,
            'addons': json.dumps(['vpc-cni', 'coredns', 'kube-proxy', 'aws-ebs-csi-driver']),
            'tags': {'Environment': 'Production', 'Team': 'Platform', 'CostCenter': 'GP001'}
        },
        {
            'account': accounts[1] if len(accounts) > 1 else accounts[0],  # HQ CTO account
            'cluster_name': 'hq-dev-eks-cluster',
            'kubernetes_version': '1.27',
            'platform_version': 'eks.8',
            'status': 'ACTIVE',
            'cluster_type': 'EC2',
            'endpoint': 'https://B2C3D4E5F6G7H8I9J0K1.gr7.us-east-1.eks.amazonaws.com',
            'vpc_id': 'vpc-0987654321fedcba0',
            'subnet_ids': json.dumps(['subnet-0987654321fedcba0', 'subnet-0987654321fedcba1']),
            'endpoint_public_access': True,
            'endpoint_private_access': False,
            'logging_enabled': False,
            'encryption_enabled': False,
            'addons': json.dumps(['vpc-cni', 'coredns', 'kube-proxy']),
            'tags': {'Environment': 'Development', 'Team': 'DevOps', 'Project': 'Innovation'}
        },
        {
            'account': accounts[2] if len(accounts) > 2 else accounts[0],  # Renewable account
            'cluster_name': 're-staging-eks-cluster',
            'kubernetes_version': '1.28',
            'platform_version': 'eks.3',
            'status': 'UPDATING',
            'cluster_type': 'Fargate',
            'endpoint': 'https://C3D4E5F6G7H8I9J0K1L2.gr7.us-west-2.eks.amazonaws.com',
            'vpc_id': 'vpc-abcdef0123456789a',
            'subnet_ids': json.dumps(['subnet-abcdef0123456789a', 'subnet-abcdef0123456789b']),
            'endpoint_public_access': False,
            'endpoint_private_access': True,
            'logging_enabled': True,
            'logging_types': json.dumps(['api', 'audit']),
            'encryption_enabled': True,
            'addons': json.dumps(['vpc-cni', 'coredns']),
            'tags': {'Environment': 'Staging', 'Team': 'Renewable', 'Compliance': 'SOX'}
        },
        {
            'account': accounts[0],  # Another Gas Power cluster
            'cluster_name': 'gp-test-eks-cluster',
            'kubernetes_version': '1.26',
            'platform_version': 'eks.12',
            'status': 'ACTIVE',
            'cluster_type': 'EC2',
            'endpoint': 'https://D4E5F6G7H8I9J0K1L2M3.gr7.us-east-1.eks.amazonaws.com',
            'vpc_id': 'vpc-fedcba0987654321f',
            'subnet_ids': json.dumps(['subnet-fedcba0987654321f']),
            'endpoint_public_access': True,
            'endpoint_private_access': False,
            'logging_enabled': False,
            'encryption_enabled': False,
            'addons': json.dumps(['vpc-cni', 'coredns', 'kube-proxy']),
            'tags': {'Environment': 'Test', 'Team': 'QA', 'Temporary': 'true'}
        }
    ]
    
    created_clusters = []
    
    for cluster_data in sample_clusters:
        # Extract tags for separate processing
        tags_data = cluster_data.pop('tags')
        
        # Create cluster
        cluster, created = EKSCluster.objects.get_or_create(
            account=cluster_data['account'],
            cluster_name=cluster_data['cluster_name'],
            defaults={
                **cluster_data,
                'arn': f"arn:aws:eks:{cluster_data['account'].region}:{cluster_data['account'].account_id}:cluster/{cluster_data['cluster_name']}",
                'created_at_aws': timezone.now() - timedelta(days=30),
                'all_tags_raw': json.dumps(tags_data)
            }
        )
        
        if created:
            print(f"✅ Created cluster: {cluster.cluster_name}")
            
            # Create tags
            for key, value in tags_data.items():
                tag, _ = EKSClusterTag.objects.get_or_create(key=key, value=value)
                cluster.tags.add(tag)
            
            created_clusters.append(cluster)
        else:
            print(f"⚠️  Cluster already exists: {cluster.cluster_name}")
    
    # Create sample node groups
    if created_clusters:
        node_groups_data = [
            {
                'cluster': created_clusters[0],  # gp-prod-eks-cluster
                'node_group_name': 'gp-prod-workers',
                'status': 'ACTIVE',
                'capacity_type': 'ON_DEMAND',
                'instance_types': json.dumps(['m5.large', 'm5.xlarge']),
                'ami_type': 'AL2_x86_64',
                'desired_size': 3,
                'min_size': 1,
                'max_size': 5,
                'disk_size': 20
            },
            {
                'cluster': created_clusters[0],  # gp-prod-eks-cluster
                'node_group_name': 'gp-prod-spot-workers',
                'status': 'ACTIVE',
                'capacity_type': 'SPOT',
                'instance_types': json.dumps(['m5.large', 'm5.xlarge', 'c5.large']),
                'ami_type': 'AL2_x86_64',
                'desired_size': 2,
                'min_size': 0,
                'max_size': 10,
                'disk_size': 20
            },
            {
                'cluster': created_clusters[1] if len(created_clusters) > 1 else created_clusters[0],  # hq-dev-eks-cluster
                'node_group_name': 'hq-dev-workers',
                'status': 'ACTIVE',
                'capacity_type': 'ON_DEMAND',
                'instance_types': json.dumps(['t3.medium']),
                'ami_type': 'AL2_x86_64',
                'desired_size': 2,
                'min_size': 1,
                'max_size': 3,
                'disk_size': 20
            }
        ]
        
        for ng_data in node_groups_data:
            node_group, created = EKSNodeGroup.objects.get_or_create(
                cluster=ng_data['cluster'],
                node_group_name=ng_data['node_group_name'],
                defaults={
                    **ng_data,
                    'arn': f"arn:aws:eks:{ng_data['cluster'].account.region}:{ng_data['cluster'].account.account_id}:nodegroup/{ng_data['cluster'].cluster_name}/{ng_data['node_group_name']}",
                    'created_at_aws': timezone.now() - timedelta(days=25),
                    'all_tags_raw': json.dumps({'NodeGroup': ng_data['node_group_name']})
                }
            )
            
            if created:
                print(f"✅ Created node group: {node_group.node_group_name}")
    
    # Create sample Fargate profiles
    if created_clusters:
        fargate_profiles_data = [
            {
                'cluster': created_clusters[0],  # gp-prod-eks-cluster
                'profile_name': 'gp-prod-fargate-profile',
                'status': 'ACTIVE',
                'selectors': json.dumps([
                    {'namespace': 'fargate-namespace'},
                    {'namespace': 'kube-system', 'labels': {'k8s-app': 'kube-dns'}}
                ])
            },
            {
                'cluster': created_clusters[2] if len(created_clusters) > 2 else created_clusters[0],  # re-staging-eks-cluster
                'profile_name': 're-staging-fargate-profile',
                'status': 'ACTIVE',
                'selectors': json.dumps([
                    {'namespace': 'default'},
                    {'namespace': 'staging-apps'}
                ])
            }
        ]
        
        for fp_data in fargate_profiles_data:
            fargate_profile, created = EKSFargateProfile.objects.get_or_create(
                cluster=fp_data['cluster'],
                profile_name=fp_data['profile_name'],
                defaults={
                    **fp_data,
                    'arn': f"arn:aws:eks:{fp_data['cluster'].account.region}:{fp_data['cluster'].account.account_id}:fargateprofile/{fp_data['cluster'].cluster_name}/{fp_data['profile_name']}",
                    'created_at_aws': timezone.now() - timedelta(days=20),
                    'all_tags_raw': json.dumps({'FargateProfile': fp_data['profile_name']})
                }
            )
            
            if created:
                print(f"✅ Created Fargate profile: {fargate_profile.profile_name}")
    
    # Update cluster counts
    for cluster in created_clusters:
        cluster.node_groups_count = cluster.node_groups.count()
        cluster.fargate_profiles_count = cluster.fargate_profiles.count()
        
        # Determine cluster type based on what it has
        if cluster.node_groups_count > 0 and cluster.fargate_profiles_count > 0:
            cluster.cluster_type = 'Hybrid'
        elif cluster.fargate_profiles_count > 0:
            cluster.cluster_type = 'Fargate'
        else:
            cluster.cluster_type = 'EC2'
        
        cluster.save()
    
    # Print summary
    total_clusters = EKSCluster.objects.count()
    total_node_groups = EKSNodeGroup.objects.count()
    total_fargate_profiles = EKSFargateProfile.objects.count()
    total_tags = EKSClusterTag.objects.count()
    
    print(f"\n📊 Sample EKS Data Summary:")
    print(f"   • Total Clusters: {total_clusters}")
    print(f"   • Total Node Groups: {total_node_groups}")
    print(f"   • Total Fargate Profiles: {total_fargate_profiles}")
    print(f"   • Total Unique Tags: {total_tags}")
    
    # Show clusters by business unit
    print(f"\n🏢 Clusters by Business Unit:")
    for account in accounts:
        cluster_count = EKSCluster.objects.filter(account=account).count()
        if cluster_count > 0:
            print(f"   • {account.business_unit}: {cluster_count} clusters")
    
    print(f"\n🚀 Ready to test EKS functionality:")
    print(f"   1. Access EKS page: http://127.0.0.1:8000/eks-clusters/")
    print(f"   2. Test filters and search")
    print(f"   3. View cluster details")
    print(f"   4. Export to Excel")
    print(f"   5. Check dashboard integration")

if __name__ == '__main__':
    create_sample_eks_data()
