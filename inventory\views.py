from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count
from django.utils import timezone
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
import boto3
import json
from datetime import datetime
import pandas as pd
from io import BytesIO

from .models import (
    AWSAccount, EC2Instance, SSMStatus, InventoryRefreshLog, InstanceTag,
    EKSCluster, EKSNodeGroup, EKSFargateProfile, EKSClusterTag
)
from .serializers import (
    AWSAccountSerializer, EC2InstanceDetailedSerializer, EC2InstanceListSerializer,
    EC2InstanceMainInventorySerializer, SSMStatusSerializer, InventoryRefreshLogSerializer,
    InventoryStatsSerializer, EKSClusterListSerializer, EKSClusterDetailedSerializer
)
from .aws_services import EC2Service, SSMService


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200


class AWSAccountViewSet(viewsets.ModelViewSet):
    queryset = AWSAccount.objects.all()
    serializer_class = AWSAccountSerializer
    pagination_class = StandardResultsSetPagination
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = AWSAccount.objects.all()

        # Search functionality
        search = self.request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(account_name__icontains=search) |
                Q(account_id__icontains=search) |
                Q(business_unit__icontains=search) |
                Q(region__icontains=search)
            )

        # Business unit filter
        business_unit = self.request.query_params.get('business_unit', None)
        if business_unit:
            queryset = queryset.filter(business_unit__iexact=business_unit)

        # Region filter
        region = self.request.query_params.get('region', None)
        if region:
            queryset = queryset.filter(region__iexact=region)

        # Status filter
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Ordering
        ordering = self.request.query_params.get('ordering', 'business_unit,account_name')
        if ordering:
            queryset = queryset.order_by(*ordering.split(','))

        return queryset

    def list(self, request, *args, **kwargs):
        """Enhanced list with statistics"""
        queryset = self.filter_queryset(self.get_queryset())

        # Get statistics
        from django.db.models import Count, Q

        total_accounts = AWSAccount.objects.count()
        filtered_count = queryset.count()
        active_accounts = queryset.filter(is_active=True).count()
        inactive_accounts = filtered_count - active_accounts

        # Business unit statistics
        bu_stats = queryset.values('business_unit').annotate(
            count=Count('id')
        ).order_by('-count')

        # Region statistics
        region_stats = queryset.values('region').annotate(
            count=Count('id')
        ).order_by('-count')

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)

            # Add statistics to response
            response.data['statistics'] = {
                'total_accounts': total_accounts,
                'filtered_count': filtered_count,
                'active_accounts': active_accounts,
                'inactive_accounts': inactive_accounts,
                'business_units': list(bu_stats),
                'regions': list(region_stats)
            }

            return response

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'results': serializer.data,
            'count': filtered_count,
            'statistics': {
                'total_accounts': total_accounts,
                'filtered_count': filtered_count,
                'active_accounts': active_accounts,
                'inactive_accounts': inactive_accounts,
                'business_units': list(bu_stats),
                'regions': list(region_stats)
            }
        })

    @action(detail=True, methods=['post'])
    def refresh_inventory(self, request, pk=None):
        """Trigger inventory refresh for a specific account"""
        account = self.get_object()
        try:
            # Create refresh log
            refresh_log = InventoryRefreshLog.objects.create(
                account=account,
                status='running'
            )

            # Initialize AWS services
            ec2_service = EC2Service(account.account_id, account.region)
            ssm_service = SSMService(account.account_id, account.region)

            # Refresh EC2 instances
            instances_data = ec2_service.get_instances()
            instances_processed = 0
            errors_count = 0

            for instance_data in instances_data:
                try:
                    # Extract flattened tags
                    flattened_tags = instance_data.pop('flattened_tags', [])

                    # Update or create EC2 instance
                    instance, created = EC2Instance.objects.update_or_create(
                        account=account,
                        instance_id=instance_data['instance_id'],
                        defaults=instance_data
                    )

                    # Update flattened tags
                    instance.tags.clear()  # Clear existing tags
                    for tag_data in flattened_tags:
                        tag, _ = InstanceTag.objects.get_or_create(
                            key=tag_data['key'],
                            value=tag_data['value']
                        )
                        instance.tags.add(tag)

                    # Update SSM status
                    ssm_data = ssm_service.get_instance_ssm_status(instance_data['instance_id'])
                    if ssm_data:
                        SSMStatus.objects.update_or_create(
                            instance=instance,
                            defaults=ssm_data
                        )

                    instances_processed += 1
                except Exception as e:
                    errors_count += 1
                    print(f"Error processing instance {instance_data.get('instance_id', 'unknown')}: {str(e)}")

            # Update refresh log
            refresh_log.status = 'completed'
            refresh_log.completed_at = timezone.now()
            refresh_log.instances_processed = instances_processed
            refresh_log.errors_count = errors_count
            refresh_log.save()

            return Response({
                'status': 'success',
                'message': f'Inventory refreshed successfully. Processed {instances_processed} instances.',
                'instances_processed': instances_processed,
                'errors_count': errors_count
            })

        except Exception as e:
            if 'refresh_log' in locals():
                refresh_log.status = 'failed'
                refresh_log.completed_at = timezone.now()
                refresh_log.error_details = str(e)
                refresh_log.save()

            return Response({
                'status': 'error',
                'message': f'Failed to refresh inventory: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EC2InstanceViewSet(viewsets.ModelViewSet):
    queryset = EC2Instance.objects.select_related('account', 'ssm_status').prefetch_related('tags').all()
    pagination_class = StandardResultsSetPagination
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'list':
            view_type = self.request.query_params.get('view', 'list')
            if view_type == 'main':
                return EC2InstanceMainInventorySerializer
            return EC2InstanceListSerializer
        return EC2InstanceDetailedSerializer

    def get_queryset(self):
        queryset = EC2Instance.objects.select_related('account', 'ssm_status').prefetch_related('tags').all()

        # Filter parameters
        account_id = self.request.query_params.get('account_id', None)
        business_unit = self.request.query_params.get('business_unit', None)
        state = self.request.query_params.get('state', None)
        instance_type = self.request.query_params.get('instance_type', None)
        env_tag = self.request.query_params.get('env_tag', None)
        uai_tag = self.request.query_params.get('uai_tag', None)
        cto_managed = self.request.query_params.get('cto_cloud_ops_managed', None)
        tag_key = self.request.query_params.get('tag_key', None)
        tag_value = self.request.query_params.get('tag_value', None)
        search = self.request.query_params.get('search', None)

        if account_id:
            queryset = queryset.filter(account__account_id=account_id)
        if business_unit:
            queryset = queryset.filter(account__business_unit__icontains=business_unit)
        if state:
            queryset = queryset.filter(state=state)
        if instance_type:
            queryset = queryset.filter(instance_type__icontains=instance_type)
        if env_tag:
            queryset = queryset.filter(env_tag__icontains=env_tag)
        if uai_tag:
            queryset = queryset.filter(uai_tag__icontains=uai_tag)
        if cto_managed:
            queryset = queryset.filter(cto_cloud_ops_managed__icontains=cto_managed)

        # Filter by specific tag key-value pairs
        if tag_key:
            if tag_value:
                queryset = queryset.filter(tags__key=tag_key, tags__value__icontains=tag_value)
            else:
                queryset = queryset.filter(tags__key=tag_key)

        # Search across multiple fields including tags
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(instance_id__icontains=search) |
                Q(private_ip_address__icontains=search) |
                Q(ami_id__icontains=search) |
                Q(subnet_id__icontains=search) |
                Q(tags__key__icontains=search) |
                Q(tags__value__icontains=search)
            ).distinct()

        return queryset

    @action(detail=False, methods=['get'])
    def export_excel(self, request):
        """Export EC2 instances to Excel with all required fields"""
        queryset = self.get_queryset()

        # Prepare data for Excel with all required fields
        data = []
        for instance in queryset:
            ssm_status = getattr(instance, 'ssm_status', None)

            # Parse security groups
            security_groups_str = ''
            try:
                if instance.security_groups:
                    sg_list = json.loads(instance.security_groups)
                    security_groups_str = ', '.join([f"{sg['name']} ({sg['id']})" for sg in sg_list])
            except (json.JSONDecodeError, TypeError):
                security_groups_str = instance.security_groups or ''

            data.append({
                'BU': instance.account.business_unit,
                'Account Name': instance.account.account_name,
                'Account ID': instance.account.account_id,
                'Region': instance.account.region,
                'Instance ID': instance.instance_id,
                'State': instance.state,
                'Name': instance.name or '',
                'OS Information': instance.os_information or '',
                'Private IP Address': instance.private_ip_address or '',
                'Instance Type': instance.instance_type,
                'V CPU': instance.vcpu_count or '',
                'Memory/Ram (GB)': instance.memory_gb or '',
                'Root EBS Volume ID': instance.root_volume_id or '',
                'Root EBS Volume Size (GB)': instance.root_volume_size_gb or '',
                'Data Disk Count': instance.data_disk_count or 0,
                'Env Tag': instance.env_tag or '',
                'Uai Tag': instance.uai_tag or '',
                'Patch Tag': instance.patch_tag or '',
                'AppEnvCfgID Tag': instance.app_env_cfg_id or '',
                'MaintenanceSchedule Tag': instance.maintenance_schedule or '',
                'Schedule Tag': instance.schedule_tag or '',
                'Backup Tag': instance.backup_tag or '',
                'CTOCloudOpsManaged Tag': instance.cto_cloud_ops_managed or '',
                'PatchProvider Tag': instance.patch_provider or '',
                'PatchGroup Tag': instance.patch_group or '',
                'PatchExempt Tag': instance.patch_exempt or '',
                'AMI': instance.ami_id or '',
                'Instance Profile': instance.instance_profile or '',
                'Security Group': security_groups_str,
                'Subnet ID': instance.subnet_id or '',
                'Subnet Name': instance.subnet_name or '',
                'SSM Agent Status': ssm_status.ping_status if ssm_status else 'Unknown',
                'SSM Agent Version': ssm_status.agent_version if ssm_status else 'N/A',
                'Is SSM Agent Latest': 'Yes' if (ssm_status and ssm_status.is_latest_version) else 'No' if ssm_status else 'Unknown',
                'All Tags': instance.all_tags_raw or '',
                'Last Updated': instance.last_updated.strftime('%Y-%m-%d %H:%M:%S'),
            })

        # Create Excel file
        df = pd.DataFrame(data)
        output = BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='EC2_Inventory_Detailed')

        output.seek(0)

        # Create response
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="ec2_inventory_detailed_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response

    @action(detail=False, methods=['get'])
    def search_by_tags(self, request):
        """Search instances by tag key-value pairs"""
        tag_filters = {}

        # Extract tag filters from query parameters
        for key, value in request.query_params.items():
            if key.startswith('tag_'):
                tag_key = key[4:]  # Remove 'tag_' prefix
                tag_filters[tag_key] = value

        if not tag_filters:
            return Response({'error': 'No tag filters provided'}, status=status.HTTP_400_BAD_REQUEST)

        # Build query
        queryset = EC2Instance.objects.select_related('account', 'ssm_status').prefetch_related('tags')

        for tag_key, tag_value in tag_filters.items():
            queryset = queryset.filter(tags__key=tag_key, tags__value__icontains=tag_value)

        queryset = queryset.distinct()

        # Paginate results
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = EC2InstanceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = EC2InstanceListSerializer(queryset, many=True)
        return Response(serializer.data)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_available_tags(request):
    """Get all available tag keys and their possible values"""
    try:
        # Get all unique tag keys
        tag_keys = InstanceTag.objects.values_list('key', flat=True).distinct().order_by('key')

        # For each key, get sample values (limit to 10 for performance)
        tags_data = {}
        for key in tag_keys:
            values = InstanceTag.objects.filter(key=key).values_list('value', flat=True).distinct()[:10]
            tags_data[key] = list(values)

        return Response({
            'tag_keys': list(tag_keys),
            'tags_with_values': tags_data,
            'total_unique_keys': len(tag_keys),
            'total_tags': InstanceTag.objects.count()
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_stats(request):
    """Get comprehensive dashboard statistics"""
    try:
        # Account statistics
        total_accounts = AWSAccount.objects.count()
        active_accounts = AWSAccount.objects.filter(is_active=True).count()

        # Instance statistics
        total_instances = EC2Instance.objects.count()
        running_instances = EC2Instance.objects.filter(state='running').count()
        stopped_instances = EC2Instance.objects.filter(state='stopped').count()

        # SSM statistics
        ssm_online = SSMStatus.objects.filter(ping_status='Online').count()
        ssm_offline = SSMStatus.objects.filter(ping_status='Offline').count()

        # EKS statistics
        total_clusters = EKSCluster.objects.count()
        active_clusters = EKSCluster.objects.filter(status='ACTIVE').count()

        # Business unit breakdown
        bu_accounts = AWSAccount.objects.values('business_unit').annotate(
            account_count=Count('id')
        ).order_by('-account_count')

        bu_instances = EC2Instance.objects.values('account__business_unit').annotate(
            instance_count=Count('id'),
            running_count=Count('id', filter=Q(state='running')),
            ssm_online_count=Count('ssm_status', filter=Q(ssm_status__ping_status='Online'))
        ).order_by('-instance_count')

        # Region breakdown
        region_stats = AWSAccount.objects.values('region').annotate(
            account_count=Count('id')
        ).order_by('-account_count')

        return Response({
            'total_accounts': total_accounts,
            'active_accounts': active_accounts,
            'inactive_accounts': total_accounts - active_accounts,
            'total_instances': total_instances,
            'running_instances': running_instances,
            'stopped_instances': stopped_instances,
            'ssm_online': ssm_online,
            'ssm_offline': ssm_offline,
            'total_clusters': total_clusters,
            'active_clusters': active_clusters,
            'business_units': list(bu_accounts),
            'regions': list(region_stats),
            'business_unit_instances': list(bu_instances),
            'health_metrics': {
                'instance_health': round((running_instances / total_instances * 100) if total_instances > 0 else 100, 1),
                'ssm_coverage': round((ssm_online / total_instances * 100) if total_instances > 0 else 0, 1),
                'cluster_health': round((active_clusters / total_clusters * 100) if total_clusters > 0 else 100, 1)
            }
        })

    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SSMStatusViewSet(viewsets.ModelViewSet):
    queryset = SSMStatus.objects.select_related('instance', 'instance__account').all()
    serializer_class = SSMStatusSerializer
    pagination_class = StandardResultsSetPagination
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = SSMStatus.objects.select_related('instance', 'instance__account').all()

        ping_status = self.request.query_params.get('ping_status', None)
        account_id = self.request.query_params.get('account_id', None)
        business_unit = self.request.query_params.get('business_unit', None)
        search = self.request.query_params.get('search', None)

        if ping_status:
            queryset = queryset.filter(ping_status=ping_status)
        if account_id:
            queryset = queryset.filter(instance__account__account_id=account_id)
        if business_unit:
            queryset = queryset.filter(instance__account__business_unit__icontains=business_unit)
        if search:
            queryset = queryset.filter(
                Q(instance__instance_id__icontains=search) |
                Q(instance__name__icontains=search) |
                Q(instance__account__account_name__icontains=search)
            ).distinct()

        return queryset


class EKSClusterViewSet(viewsets.ModelViewSet):
    queryset = EKSCluster.objects.select_related('account').prefetch_related('tags', 'node_groups', 'fargate_profiles').all()
    pagination_class = StandardResultsSetPagination
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'list':
            return EKSClusterListSerializer
        return EKSClusterDetailedSerializer

    def get_queryset(self):
        queryset = EKSCluster.objects.select_related('account').prefetch_related('tags', 'node_groups', 'fargate_profiles').all()

        # Filter parameters
        account_id = self.request.query_params.get('account_id', None)
        business_unit = self.request.query_params.get('business_unit', None)
        status = self.request.query_params.get('status', None)
        cluster_type = self.request.query_params.get('cluster_type', None)
        kubernetes_version = self.request.query_params.get('kubernetes_version', None)
        search = self.request.query_params.get('search', None)

        if account_id:
            queryset = queryset.filter(account__account_id=account_id)
        if business_unit:
            queryset = queryset.filter(account__business_unit__icontains=business_unit)
        if status:
            queryset = queryset.filter(status=status)
        if cluster_type:
            queryset = queryset.filter(cluster_type=cluster_type)
        if kubernetes_version:
            queryset = queryset.filter(kubernetes_version__icontains=kubernetes_version)

        # Search across multiple fields
        if search:
            queryset = queryset.filter(
                Q(cluster_name__icontains=search) |
                Q(kubernetes_version__icontains=search) |
                Q(platform_version__icontains=search) |
                Q(endpoint__icontains=search) |
                Q(tags__key__icontains=search) |
                Q(tags__value__icontains=search)
            ).distinct()

        return queryset

    @action(detail=False, methods=['get'])
    def export_excel(self, request):
        """Export EKS clusters to Excel with all required fields"""
        queryset = self.get_queryset()

        # Prepare data for Excel
        data = []
        for cluster in queryset:
            # Parse addons
            addons_str = ''
            try:
                if cluster.addons:
                    addons_list = json.loads(cluster.addons)
                    addons_str = ', '.join(addons_list)
            except (json.JSONDecodeError, TypeError):
                addons_str = cluster.addons or ''

            # Parse Fargate profiles
            fargate_str = ''
            try:
                if cluster.fargate_profile_names:
                    fargate_list = json.loads(cluster.fargate_profile_names)
                    fargate_str = ', '.join(fargate_list)
            except (json.JSONDecodeError, TypeError):
                fargate_str = cluster.fargate_profile_names or ''

            data.append({
                'BU': cluster.account.business_unit,
                'Account Name': cluster.account.account_name,
                'Account ID': cluster.account.account_id,
                'Region': cluster.account.region,
                'Cluster Name': cluster.cluster_name,
                'Kubernetes Version': cluster.kubernetes_version,
                'EKS Version': cluster.platform_version,
                'Status': cluster.status,
                'Cluster Type': cluster.cluster_type,
                'Addons': addons_str,
                'Node Groups': cluster.node_groups_count,
                'Fargate Profiles': cluster.fargate_profiles_count,
                'Endpoint': cluster.endpoint,
                'VPC ID': cluster.vpc_id,
                'Public Access': 'Yes' if cluster.endpoint_public_access else 'No',
                'Private Access': 'Yes' if cluster.endpoint_private_access else 'No',
                'Logging Enabled': 'Yes' if cluster.logging_enabled else 'No',
                'Encryption Enabled': 'Yes' if cluster.encryption_enabled else 'No',
                'Created At': cluster.created_at_aws.strftime('%Y-%m-%d %H:%M:%S') if cluster.created_at_aws else '',
                'ARN': cluster.arn,
                'All Tags': cluster.all_tags_raw or '',
                'Last Updated': cluster.last_updated.strftime('%Y-%m-%d %H:%M:%S'),
            })

        # Create Excel file
        df = pd.DataFrame(data)
        output = BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='EKS_Clusters_Detailed')

        output.seek(0)

        # Create response
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="eks_clusters_detailed_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        return response





@api_view(['POST'])
@permission_classes([IsAuthenticated])
def refresh_all_inventory(request):
    """Refresh inventory for all active accounts"""
    try:
        active_accounts = AWSAccount.objects.filter(is_active=True)
        total_processed = 0
        total_errors = 0

        for account in active_accounts:
            try:
                # Create refresh log
                refresh_log = InventoryRefreshLog.objects.create(
                    account=account,
                    status='running'
                )

                # Initialize AWS services
                ec2_service = EC2Service(account.account_id, account.region)
                ssm_service = SSMService(account.account_id, account.region)

                # Refresh EC2 instances
                instances_data = ec2_service.get_instances()
                instances_processed = 0
                errors_count = 0

                for instance_data in instances_data:
                    try:
                        # Extract flattened tags
                        flattened_tags = instance_data.pop('flattened_tags', [])

                        # Update or create EC2 instance
                        instance, created = EC2Instance.objects.update_or_create(
                            account=account,
                            instance_id=instance_data['instance_id'],
                            defaults=instance_data
                        )

                        # Update flattened tags
                        instance.tags.clear()  # Clear existing tags
                        for tag_data in flattened_tags:
                            tag, _ = InstanceTag.objects.get_or_create(
                                key=tag_data['key'],
                                value=tag_data['value']
                            )
                            instance.tags.add(tag)

                        # Update SSM status
                        ssm_data = ssm_service.get_instance_ssm_status(instance_data['instance_id'])
                        if ssm_data:
                            SSMStatus.objects.update_or_create(
                                instance=instance,
                                defaults=ssm_data
                            )

                        instances_processed += 1
                    except Exception as e:
                        errors_count += 1
                        print(f"Error processing instance {instance_data.get('instance_id', 'unknown')}: {str(e)}")

                # Update refresh log
                refresh_log.status = 'completed'
                refresh_log.completed_at = timezone.now()
                refresh_log.instances_processed = instances_processed
                refresh_log.errors_count = errors_count
                refresh_log.save()

                total_processed += instances_processed
                total_errors += errors_count

            except Exception as e:
                if 'refresh_log' in locals():
                    refresh_log.status = 'failed'
                    refresh_log.completed_at = timezone.now()
                    refresh_log.error_details = str(e)
                    refresh_log.save()

                total_errors += 1
                print(f"Error processing account {account.account_id}: {str(e)}")

        return Response({
            'status': 'success',
            'message': f'Inventory refresh completed. Processed {total_processed} instances across {active_accounts.count()} accounts.',
            'total_processed': total_processed,
            'total_errors': total_errors
        })

    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'Failed to refresh inventory: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Template views for the frontend
@login_required
def dashboard_view(request):
    """Dashboard page"""
    return render(request, 'inventory/dashboard.html')


@login_required
def ec2_instances_view(request):
    """EC2 instances page"""
    return render(request, 'inventory/ec2_instances.html')


@login_required
def ssm_status_view(request):
    """SSM status page"""
    return render(request, 'inventory/ssm_status.html')


@login_required
def accounts_view(request):
    """Accounts management page"""
    return render(request, 'inventory/accounts.html')


@login_required
def eks_clusters_view(request):
    """EKS clusters page"""
    return render(request, 'inventory/eks_clusters.html')


@login_required
def placeholder_view(request, feature_name):
    """Placeholder page for unimplemented features"""
    return render(request, 'inventory/placeholder.html', {
        'feature_name': feature_name
    })
