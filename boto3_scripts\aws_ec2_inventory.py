#version 1.2
import boto3
import logging
import re
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side
from datetime import datetime
import time
import sys
from io import StringIO
import warnings
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import botocore
from botocore.config import Config
from botocore.exceptions import BotoCoreError, ClientError
warnings.filterwarnings('ignore')
print ("Script Started ..." )
now1 = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

# Configure logging
logging.basicConfig(filename=f'aws_ec2_inventory_2.2-{now1}.log', level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')

def get_instance_ids(ec2_client):
    instance_ids = []
    paginator = ec2_client.get_paginator('describe_instances')
    try:
        for page in paginator.paginate(
            # Filters=[{'Name': 'instance-state-name', 'Values': ['running']}]
        ):
            for reservation in page['Reservations']:
                for instance in reservation['Instances']:
                    instance_id = instance['InstanceId']
                    instance_ids.append(instance_id)
    
        return instance_ids
    except (BotoCoreError, ClientError) as e:
        logging.error(f"Error getting instance ids: {str(e)}")
        return []




def get_instance_info(ec2_client, ssm_client, instance):
    instance_id = instance['InstanceId']
    instance_type = instance['InstanceType']
    state = instance['State']['Name']
    tags = instance.get('Tags', [])
    tags_str = ", ".join([f"{tag['Key']}={tag['Value']}" for tag in tags])
    name = next((tag['Value'] for tag in tags if tag['Key'] == 'Name'), '')
    env = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'env'), '')
    uai = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'uai'), '')
    patch = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'patch'), '')
    AppEnvCfgID = next((tag['Value'] for tag in tags if tag['Key'] == 'AppEnvCfgID'), '')
    MaintenanceSchedule = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'maintenanceschedule'), '')
    Schedule = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'schedule'), '')
    Backup = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'backup'), '')
    CTOCloudOpsManaged = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'ctocloudopsmanaged'), 'NotAssigned')
    PatchProvider = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'patchprovider'), 'NotAssigned')
    PatchGroup = next(
    (tag['Value'] for tag in tags if tag['Key'].lower().replace(" ", "") == 'patchgroup'),
    'NotAssigned')
    PatchExempt = next((tag['Value'] for tag in tags if tag['Key'].lower() == 'patchexempt'), 'NotAssigned')
    os_info = get_os_info(ssm_client, instance_id)
    private_dns_name = instance.get('PrivateDnsName', 'N/A')
    private_ip_address = instance.get('PrivateIpAddress', 'N/A')
    return {
        'Instance ID': instance_id,
        'Instance Type': instance_type,
        'State': state,
        'Name': name,
        'Env': env,
        'UAI': uai,
        'Patch': patch,
        'OS Information': os_info,
        'Private Dns Name': private_dns_name,
        'Private IP Address': private_ip_address,
        'AppEnvCfgID': AppEnvCfgID,
        'MaintenanceSchedule': MaintenanceSchedule,
        'Schedule': Schedule,
        'Backup': Backup,
        'CTOCloudOpsManaged': CTOCloudOpsManaged,
        'PatchProvider': PatchProvider,
        'PatchGroup':PatchGroup,
        'PatchExempt': PatchExempt,
        'all_tags': tags_str
    }

def get_os_info(ssm_client, instance_id):
    response = ssm_client.describe_instance_information(
        InstanceInformationFilterList=[{
            'key': 'InstanceIds',
            'valueSet': [instance_id]
        }]
    )
    if not response['InstanceInformationList']:
        return 'No_SSM_Response'
    os_info = response['InstanceInformationList'][0]
    platform_type = os_info.get('PlatformType', 'Unknown')
    os_name = os_info.get('PlatformName', 'No_SSMAgent_Data')
    os_version = os_info.get('PlatformVersion', ' ')
    return f"{os_name}_{os_version}"

# Modify to include BU in role data
def read_roles():
    roles = []
    with open('ge_vn_accounts.txt', 'r') as file:
        for line in file.readlines():
            account_id, region, bu = line.strip().split(',')
            roles.append((account_id, region, bu))
    return roles

def save_to_excel(main_data, access_denied_data):
    # Add 'BU' to the headers
    headers_main = ["BU", "Account Name", "Account ID", "Region","Instance ID",  "State", "Name", "OS Information", 
                    "Private IP Address", "Instance Type", "V CPU", "Memory/Ram (GB)", "Root EBS Volume ID", "Root EBS Volume Size (GB)","Data Disk Count", "Env Tag", "Uai Tag", "Patch Tag", "AppEnvCfgID Tag", "MaintenanceS chedule Tag", "Schedule Tag", "Backup Tag", "CTOCloudOpsManaged Tag", "PatchProvider Tag", "PatchGroup Tag", "PatchExempt Tag", "All Tags"]
    
    df_main = pd.DataFrame(main_data, columns=headers_main)
    df_access_denied = pd.DataFrame(access_denied_data, columns=["Role ARN", "Status", "Reason"])

    now = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_file = f'AWS_EC2_INVENTORY_REPORT_{now}.xlsx'
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df_main.to_excel(writer, index=False, sheet_name='EC2_INVENTORY_AWS')
        df_access_denied.to_excel(writer, index=False, sheet_name='ACCESS_DENIED')

        wb = writer.book
        ws_main = wb['EC2_INVENTORY_AWS']
        ws_access_denied = wb['ACCESS_DENIED']

        fill = PatternFill(start_color='017054', end_color='017054', fill_type='solid')
        red_font = Font(color='FF0000')

        for cell in ws_main[1]:
            cell.fill = fill
            cell.font = Font(bold=True, color='000000')

        for row in ws_main.iter_rows(min_row=2, max_row=ws_main.max_row, min_col=1, max_col=ws_main.max_column):
            for cell in row:
                cell.border = Border(left=Side(style='thin'), 
                                     right=Side(style='thin'), 
                                     top=Side(style='thin'), 
                                     bottom=Side(style='thin'))
                
    logging.info(f"Excel file saved: {output_file}")

# Adjust to pass BU
def process_role(account_id, role_region, bu):
    print(f"switched to account {account_id, role_region, bu}")
    main_data = []
    access_denied_data = []
    # no_instance_data = []
    try:
        profile_name = f"support-{account_id}"
        print(profile_name)
        session = boto3.session.Session(profile_name=profile_name, region_name=role_region)
        iam_client = session.client('iam', verify=False)
        try:
            account_alias = iam_client.list_account_aliases()['AccountAliases'][0]
        except KeyError:
            account_alias = 'Unknown'
        ec2_client = session.client('ec2', verify=False)
        ssm_client = session.client('ssm', verify=False)
        instance_ids = get_instance_ids(ec2_client)

        for instance_id in instance_ids:
            response = ec2_client.describe_instances(InstanceIds=[instance_id])
            if response['Reservations'] and response['Reservations'][0]['Instances']:
                root_device_name = response['Reservations'][0]['Instances'][0]['RootDeviceName']
                block_devices = response['Reservations'][0]['Instances'][0]['BlockDeviceMappings']
                instance = response['Reservations'][0]['Instances'][0]
                instance_info = get_instance_info(ec2_client, ssm_client, instance)
                instance_type_i = instance_info['Instance Type']
                response_2 = ec2_client.describe_instance_types(InstanceTypes=[instance_type_i])
                instance_info_2 = response_2["InstanceTypes"][0]
                vcpu_count = instance_info_2["VCpuInfo"].get("DefaultVCpus", 'N/A')
                memory_gb = round(instance_info_2["MemoryInfo"].get("SizeInMiB", 0) / 1024, 2) 
                attached_ebs_count = sum(1 for device in block_devices if device['DeviceName'] != root_device_name)
                for device in block_devices:
                    if device['DeviceName'] == root_device_name:
                        volume_id = device['Ebs']['VolumeId']
                        volume_response = ec2_client.describe_volumes(VolumeIds=[volume_id])
                        volume_size = volume_response['Volumes'][0]['Size']
                main_data.append([bu, account_alias, account_id, role_region, instance_info['Instance ID'], instance_info['State'],
                                  instance_info['Name'], instance_info['OS Information'], 
                                  instance_info['Private IP Address'], instance_info['Instance Type'], vcpu_count, memory_gb, volume_id if volume_id else 'N/A',
                                  volume_size if volume_size else 'N/A', attached_ebs_count if attached_ebs_count else '0', instance_info['Env'], instance_info['UAI'], instance_info['Patch'],
                                  instance_info['AppEnvCfgID'], instance_info['MaintenanceSchedule'], instance_info['Schedule'], instance_info['Backup'], instance_info['CTOCloudOpsManaged'], instance_info['PatchProvider'], instance_info['PatchGroup'], instance_info['PatchExempt'], instance_info['all_tags']])
                print(bu, account_alias, account_id, role_region, instance_info['Instance ID'], instance_info['State'],
                                  instance_info['Name'], instance_info['OS Information'], 
                                  instance_info['Private IP Address'], instance_info['Instance Type'], vcpu_count, memory_gb, volume_id if volume_id else 'N/A',
                                  volume_size if volume_size else 'N/A', attached_ebs_count if attached_ebs_count else 'N/A', instance_info['Env'], instance_info['UAI'], instance_info['Patch'])

    except Exception as e:
        access_denied_data.append([account_id, "Access Denied", str(e)])
        logging.error(f"Error processing role {account_id} in {role_region}: {str(e)}")

    return main_data, access_denied_data

def main():
    start_time = time.time()
    logging.info(f"Script started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    roles = read_roles()

    main_data = []
    access_denied_data = []

    with ThreadPoolExecutor(max_workers=50) as executor:
        futures = {executor.submit(process_role, account_id, role_region, bu): (account_id, role_region, bu) for account_id, role_region, bu in roles}
        for future in as_completed(futures):
            account_id, role_region, bu = futures[future]
            try:
                data = future.result()
                main_data.extend(data[0])
                access_denied_data.extend(data[1])
            except Exception as e:
                logging.error(f"Error processing role {account_id} in {role_region}: {str(e)}")

    save_to_excel(main_data, access_denied_data)
    logging.info(f"Script ended at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print ("Script execution completed ...")

if __name__ == "__main__":
    main()
