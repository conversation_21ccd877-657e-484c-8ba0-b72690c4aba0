"""
EKS-related models for AWS inventory system
Contains EKSCluster, EKSNodeGroup, EKSFargateProfile, and EKSClusterTag models
"""
from django.db import models
from django.utils import timezone
from .base import AWSAccount


class EKSClusterTag(models.Model):
    """Model to store flattened EKS cluster tags as key-value pairs"""
    key = models.CharField(max_length=255, db_index=True)
    value = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['key', 'value']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['key', 'value']),
        ]

    def __str__(self):
        return f"{self.key}={self.value}"


class EKSCluster(models.Model):
    """Model to store EKS cluster information with all metadata"""
    STATUS_CHOICES = [
        ('CREATING', 'Creating'),
        ('ACTIVE', 'Active'),
        ('DELETING', 'Deleting'),
        ('FAILED', 'Failed'),
        ('UPDATING', 'Updating'),
    ]

    CLUSTER_TYPE_CHOICES = [
        ('EC2', 'EC2'),
        ('Fargate', 'Fargate'),
        ('Hybrid', 'Hybrid (EC2 + Fargate)'),
    ]

    # Core identification
    account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE, related_name='eks_clusters')
    cluster_name = models.CharField(max_length=255, db_index=True)
    arn = models.CharField(max_length=500, unique=True)

    # Versions and status
    kubernetes_version = models.CharField(max_length=20, blank=True)
    platform_version = models.CharField(max_length=50, blank=True)  # EKS version
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, db_index=True)

    # Cluster configuration
    endpoint = models.URLField(blank=True)
    cluster_type = models.CharField(max_length=20, choices=CLUSTER_TYPE_CHOICES, default='EC2')

    # Network configuration
    vpc_id = models.CharField(max_length=50, blank=True)
    subnet_ids = models.TextField(blank=True)  # JSON array of subnet IDs
    security_group_ids = models.TextField(blank=True)  # JSON array of security group IDs
    endpoint_public_access = models.BooleanField(default=True)
    endpoint_private_access = models.BooleanField(default=False)
    public_access_cidrs = models.TextField(blank=True)  # JSON array of CIDR blocks

    # Service configuration
    service_ipv4_cidr = models.CharField(max_length=50, blank=True)
    service_ipv6_cidr = models.CharField(max_length=50, blank=True)
    ip_family = models.CharField(max_length=10, blank=True)  # ipv4 or ipv6

    # Logging configuration
    logging_enabled = models.BooleanField(default=False)
    logging_types = models.TextField(blank=True)  # JSON array of log types

    # Encryption configuration
    encryption_enabled = models.BooleanField(default=False)
    encryption_key_arn = models.CharField(max_length=500, blank=True)

    # Identity and access
    role_arn = models.CharField(max_length=500, blank=True)
    identity_oidc_issuer = models.URLField(blank=True)

    # Addons and features
    addons = models.TextField(blank=True)  # JSON array of addon names
    fargate_profile_names = models.TextField(blank=True)  # JSON array of Fargate profile names

    # Node groups information (will be populated separately)
    node_groups_count = models.IntegerField(default=0)
    fargate_profiles_count = models.IntegerField(default=0)

    # Flattened tags relationship
    tags = models.ManyToManyField(EKSClusterTag, blank=True, related_name='clusters')
    all_tags_raw = models.TextField(blank=True)  # Raw tags string for backup

    # Timestamps
    created_at_aws = models.DateTimeField(null=True, blank=True)  # When created in AWS
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['account', 'cluster_name']
        ordering = ['-last_updated']
        indexes = [
            models.Index(fields=['account', 'cluster_name']),
            models.Index(fields=['status', 'cluster_type']),
            models.Index(fields=['kubernetes_version']),
        ]

    def __str__(self):
        return f"{self.cluster_name} ({self.kubernetes_version}) - {self.account.account_name}"

    def get_business_unit(self):
        """Get business unit from account"""
        return self.account.business_unit

    def get_account_name(self):
        """Get account name"""
        return self.account.account_name

    def get_account_id(self):
        """Get account ID"""
        return self.account.account_id

    def get_region(self):
        """Get region"""
        return self.account.region


class EKSNodeGroup(models.Model):
    """Model to store EKS node group information"""
    STATUS_CHOICES = [
        ('CREATING', 'Creating'),
        ('ACTIVE', 'Active'),
        ('UPDATING', 'Updating'),
        ('DELETING', 'Deleting'),
        ('CREATE_FAILED', 'Create Failed'),
        ('DELETE_FAILED', 'Delete Failed'),
        ('DEGRADED', 'Degraded'),
    ]

    AMI_TYPE_CHOICES = [
        ('AL2_x86_64', 'Amazon Linux 2 (x86_64)'),
        ('AL2_x86_64_GPU', 'Amazon Linux 2 GPU (x86_64)'),
        ('AL2_ARM_64', 'Amazon Linux 2 (ARM64)'),
        ('CUSTOM', 'Custom'),
        ('BOTTLEROCKET_ARM_64', 'Bottlerocket (ARM64)'),
        ('BOTTLEROCKET_x86_64', 'Bottlerocket (x86_64)'),
    ]

    CAPACITY_TYPE_CHOICES = [
        ('ON_DEMAND', 'On Demand'),
        ('SPOT', 'Spot'),
    ]

    # Core identification
    cluster = models.ForeignKey(EKSCluster, on_delete=models.CASCADE, related_name='node_groups')
    node_group_name = models.CharField(max_length=255, db_index=True)
    arn = models.CharField(max_length=500, unique=True)

    # Status and configuration
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, db_index=True)
    capacity_type = models.CharField(max_length=20, choices=CAPACITY_TYPE_CHOICES, default='ON_DEMAND')

    # Instance configuration
    instance_types = models.TextField(blank=True)  # JSON array of instance types
    ami_type = models.CharField(max_length=50, choices=AMI_TYPE_CHOICES, blank=True)
    node_role_arn = models.CharField(max_length=500, blank=True)

    # Scaling configuration
    desired_size = models.IntegerField(default=0)
    min_size = models.IntegerField(default=0)
    max_size = models.IntegerField(default=0)

    # Network configuration
    subnet_ids = models.TextField(blank=True)  # JSON array of subnet IDs
    remote_access_enabled = models.BooleanField(default=False)
    remote_access_source_sg = models.TextField(blank=True)  # JSON array of security group IDs

    # Launch template
    launch_template_id = models.CharField(max_length=50, blank=True)
    launch_template_version = models.CharField(max_length=20, blank=True)

    # Disk configuration
    disk_size = models.IntegerField(null=True, blank=True)  # In GB

    # Flattened tags relationship
    tags = models.ManyToManyField(EKSClusterTag, blank=True, related_name='node_groups')
    all_tags_raw = models.TextField(blank=True)

    # Timestamps
    created_at_aws = models.DateTimeField(null=True, blank=True)
    modified_at_aws = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['cluster', 'node_group_name']
        ordering = ['-last_updated']
        indexes = [
            models.Index(fields=['cluster', 'node_group_name']),
            models.Index(fields=['status', 'capacity_type']),
        ]

    def __str__(self):
        return f"{self.node_group_name} ({self.cluster.cluster_name}) - {self.status}"


class EKSFargateProfile(models.Model):
    """Model to store EKS Fargate profile information"""
    STATUS_CHOICES = [
        ('CREATING', 'Creating'),
        ('ACTIVE', 'Active'),
        ('DELETING', 'Deleting'),
        ('CREATE_FAILED', 'Create Failed'),
        ('DELETE_FAILED', 'Delete Failed'),
    ]

    # Core identification
    cluster = models.ForeignKey(EKSCluster, on_delete=models.CASCADE, related_name='fargate_profiles')
    profile_name = models.CharField(max_length=255, db_index=True)
    arn = models.CharField(max_length=500, unique=True)

    # Configuration
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, db_index=True)
    pod_execution_role_arn = models.CharField(max_length=500, blank=True)

    # Network configuration
    subnet_ids = models.TextField(blank=True)  # JSON array of subnet IDs

    # Selectors (JSON format)
    selectors = models.TextField(blank=True)  # JSON array of namespace/label selectors

    # Flattened tags relationship
    tags = models.ManyToManyField(EKSClusterTag, blank=True, related_name='fargate_profiles')
    all_tags_raw = models.TextField(blank=True)

    # Timestamps
    created_at_aws = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['cluster', 'profile_name']
        ordering = ['-last_updated']

    def __str__(self):
        return f"{self.profile_name} ({self.cluster.cluster_name}) - {self.status}"
