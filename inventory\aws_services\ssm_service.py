"""
SSM service for AWS inventory operations
Handles SSM status monitoring and related operations
"""
import logging
from datetime import datetime
from botocore.exceptions import BotoCoreError, ClientError
from .base import AWSServiceBase


logger = logging.getLogger(__name__)


class SSMService(AWSServiceBase):
    """Service for SSM status operations"""
    
    def __init__(self, account_id, region='us-east-1'):
        super().__init__(account_id, region)
        self._ssm_client = None
    
    @property
    def ssm_client(self):
        """Get SSM client"""
        if not self._ssm_client:
            self._ssm_client = self.get_client('ssm')
        return self._ssm_client
    
    def get_instance_ssm_status(self, instance_id):
        """Get SSM status for a specific instance"""
        try:
            response = self.ssm_client.describe_instance_information(
                InstanceInformationFilterList=[{
                    'key': 'InstanceIds',
                    'valueSet': [instance_id]
                }]
            )
            
            if not response['InstanceInformationList']:
                return {
                    'ping_status': 'Inactive',
                    'agent_version': 'N/A',
                    'is_latest_version': None,
                    'last_ping_datetime': None,
                    'platform_name': 'N/A',
                    'platform_type': 'N/A',
                    'ssm_configured': False
                }
            
            instance_info = response['InstanceInformationList'][0]
            
            # Process last ping datetime
            last_ping = instance_info.get('LastPingDateTime')
            if last_ping:
                last_ping = self.format_datetime(last_ping)
            
            return {
                'ping_status': instance_info.get('PingStatus', 'Unknown'),
                'agent_version': instance_info.get('AgentVersion', 'N/A'),
                'is_latest_version': instance_info.get('IsLatestVersion'),
                'last_ping_datetime': last_ping,
                'platform_name': instance_info.get('PlatformName', 'N/A'),
                'platform_type': instance_info.get('PlatformType', 'N/A'),
                'ssm_configured': True
            }
        
        except Exception as e:
            logger.error(f"Error getting SSM status for {instance_id}: {str(e)}")
            return None
    
    def get_all_instances_ssm_status(self):
        """Get SSM status for all instances in the account"""
        try:
            instances_data = []
            
            # Use pagination to get all instances
            paginator = self.ssm_client.get_paginator('describe_instance_information')
            
            for page in paginator.paginate():
                for instance_info in page.get('InstanceInformationList', []):
                    instance_id = instance_info.get('InstanceId')
                    
                    # Process last ping datetime
                    last_ping = instance_info.get('LastPingDateTime')
                    if last_ping:
                        last_ping = self.format_datetime(last_ping)
                    
                    instances_data.append({
                        'instance_id': instance_id,
                        'ping_status': instance_info.get('PingStatus', 'Unknown'),
                        'agent_version': instance_info.get('AgentVersion', 'N/A'),
                        'is_latest_version': instance_info.get('IsLatestVersion'),
                        'last_ping_datetime': last_ping,
                        'platform_name': instance_info.get('PlatformName', 'N/A'),
                        'platform_type': instance_info.get('PlatformType', 'N/A'),
                        'ssm_configured': True
                    })
            
            return instances_data
        
        except (BotoCoreError, ClientError) as e:
            logger.error(f"Error getting SSM status for all instances: {str(e)}")
            return self.handle_aws_error(e, 'get_all_instances_ssm_status')
    
    def check_ssm_compliance(self, instance_id):
        """Check SSM compliance status for an instance"""
        try:
            response = self.ssm_client.list_compliance_items(
                ResourceIds=[instance_id],
                ResourceTypes=['ManagedInstance']
            )
            
            compliance_items = response.get('ComplianceItems', [])
            
            # Process compliance data
            compliance_summary = {
                'compliant': 0,
                'non_compliant': 0,
                'unspecified': 0,
                'details': []
            }
            
            for item in compliance_items:
                status = item.get('Status', 'UNSPECIFIED')
                compliance_type = item.get('ComplianceType', 'Unknown')
                
                if status == 'COMPLIANT':
                    compliance_summary['compliant'] += 1
                elif status == 'NON_COMPLIANT':
                    compliance_summary['non_compliant'] += 1
                else:
                    compliance_summary['unspecified'] += 1
                
                compliance_summary['details'].append({
                    'type': compliance_type,
                    'status': status,
                    'severity': item.get('Severity', 'UNSPECIFIED'),
                    'title': item.get('Title', ''),
                    'execution_summary': item.get('ExecutionSummary', {})
                })
            
            return compliance_summary
        
        except Exception as e:
            logger.error(f"Error checking SSM compliance for {instance_id}: {str(e)}")
            return None
    
    def get_patch_compliance(self, instance_id):
        """Get patch compliance information for an instance"""
        try:
            response = self.ssm_client.describe_instance_patch_states(
                InstanceIds=[instance_id]
            )
            
            if not response.get('InstancePatchStates'):
                return None
            
            patch_state = response['InstancePatchStates'][0]
            
            return {
                'instance_id': instance_id,
                'patch_group': patch_state.get('PatchGroup', ''),
                'baseline_id': patch_state.get('BaselineId', ''),
                'snapshot_id': patch_state.get('SnapshotId', ''),
                'installed_count': patch_state.get('InstalledCount', 0),
                'installed_other_count': patch_state.get('InstalledOtherCount', 0),
                'missing_count': patch_state.get('MissingCount', 0),
                'failed_count': patch_state.get('FailedCount', 0),
                'not_applicable_count': patch_state.get('NotApplicableCount', 0),
                'operation_start_time': self.format_datetime(patch_state.get('OperationStartTime')),
                'operation_end_time': self.format_datetime(patch_state.get('OperationEndTime')),
                'operation': patch_state.get('Operation', ''),
                'reboot_option': patch_state.get('RebootOption', '')
            }
        
        except Exception as e:
            logger.error(f"Error getting patch compliance for {instance_id}: {str(e)}")
            return None
