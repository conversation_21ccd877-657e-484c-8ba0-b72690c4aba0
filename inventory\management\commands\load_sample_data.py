from django.core.management.base import BaseCommand
from django.utils import timezone
from inventory.models import AWSAccount, EC2Instance, SSMStatus, InstanceTag
import random
import json
from datetime import datetime, timedelta


class Command(BaseCommand):
    help = 'Load sample data for testing the AWS inventory application'

    def handle(self, *args, **options):
        self.stdout.write('Loading sample data...')
        
        # Create sample AWS accounts
        accounts_data = [
            {
                'account_id': '************',
                'account_name': 'Production Account',
                'business_unit': 'Engineering',
                'region': 'us-east-1',
                'is_active': True
            },
            {
                'account_id': '************',
                'account_name': 'Development Account',
                'business_unit': 'Engineering',
                'region': 'us-west-2',
                'is_active': True
            },
            {
                'account_id': '************',
                'account_name': 'Testing Account',
                'business_unit': 'QA',
                'region': 'eu-west-1',
                'is_active': True
            },
            {
                'account_id': '************',
                'account_name': 'Staging Account',
                'business_unit': 'Engineering',
                'region': 'us-east-1',
                'is_active': True
            }
        ]
        
        accounts = []
        for account_data in accounts_data:
            account, created = AWSAccount.objects.get_or_create(
                account_id=account_data['account_id'],
                defaults=account_data
            )
            accounts.append(account)
            if created:
                self.stdout.write(f'Created account: {account.account_name}')
        
        # Create sample EC2 instances with all required fields
        instance_types = ['t3.micro', 't3.small', 't3.medium', 'm5.large', 'm5.xlarge', 'c5.large']
        states = ['running', 'stopped', 'pending']
        environments = ['prod', 'dev', 'test', 'staging']
        os_types = ['Amazon Linux 2_4.14.252-195.483.amzn2.x86_64', 'Ubuntu_20.04', 'Windows_Server_2019', 'CentOS_7.9']
        amis = ['ami-0abcdef1234567890', 'ami-0123456789abcdef0', 'ami-0987654321fedcba0']

        instance_counter = 1
        for account in accounts:
            # Create 5-15 instances per account
            num_instances = random.randint(5, 15)

            for i in range(num_instances):
                # Generate security groups
                security_groups = [
                    {'id': f'sg-{random.randint(*********, *********):09x}', 'name': f'security-group-{random.randint(1, 10)}'},
                    {'id': f'sg-{random.randint(*********, *********):09x}', 'name': 'default'}
                ]

                instance_data = {
                    'account': account,
                    'instance_id': f'i-{random.randint(***************, ***************):015x}',
                    'instance_type': random.choice(instance_types),
                    'state': random.choice(states),
                    'name': f'Server-{instance_counter:03d}',

                    # Network information
                    'private_ip_address': f'10.0.{random.randint(1, 255)}.{random.randint(1, 255)}',
                    'private_dns_name': f'ip-10-0-{random.randint(1, 255)}-{random.randint(1, 255)}.ec2.internal',
                    'subnet_id': f'subnet-{random.randint(*********, *********):09x}',
                    'subnet_name': f'Private-Subnet-{random.randint(1, 10)}',
                    'security_groups': json.dumps(security_groups),

                    # Instance specifications
                    'vcpu_count': random.choice([1, 2, 4, 8]),
                    'memory_gb': random.choice([1, 2, 4, 8, 16, 32]),
                    'ami_id': random.choice(amis),
                    'instance_profile': f'arn:aws:iam::{account.account_id}:instance-profile/EC2-Role-{random.randint(1, 5)}',

                    # Storage information
                    'root_volume_id': f'vol-{random.randint(***************, ***************):015x}',
                    'root_volume_size_gb': random.choice([8, 20, 50, 100]),
                    'data_disk_count': random.randint(0, 3),

                    # OS information
                    'os_information': random.choice(os_types),
                    'platform_type': random.choice(['Linux/UNIX', 'Windows']),

                    # Tag fields
                    'env_tag': random.choice(environments),
                    'uai_tag': f'UAI{random.randint(1000, 9999)}',
                    'patch_tag': random.choice(['auto', 'manual', 'exempt']),
                    'app_env_cfg_id': f'APP{random.randint(100, 999)}',
                    'maintenance_schedule': random.choice(['weekend', 'weeknight', 'manual']),
                    'schedule_tag': random.choice(['24x7', 'business-hours', 'dev-hours']),
                    'backup_tag': random.choice(['daily', 'weekly', 'none']),
                    'cto_cloud_ops_managed': random.choice(['Yes', 'No', 'NotAssigned']),
                    'patch_provider': random.choice(['AWS', 'Manual', 'NotAssigned']),
                    'patch_group': random.choice(['Group-A', 'Group-B', 'Group-C', 'NotAssigned']),
                    'patch_exempt': random.choice(['Yes', 'No', 'NotAssigned']),
                    'all_tags_raw': f'Name=Server-{instance_counter:03d}, Environment={random.choice(environments)}, Owner=TeamA'
                }
                
                instance, created = EC2Instance.objects.get_or_create(
                    account=account,
                    instance_id=instance_data['instance_id'],
                    defaults=instance_data
                )

                if created:
                    # Create flattened tags
                    sample_tags = [
                        {'key': 'Name', 'value': f'Server-{instance_counter:03d}'},
                        {'key': 'Environment', 'value': instance_data['env_tag']},
                        {'key': 'Owner', 'value': random.choice(['TeamA', 'TeamB', 'TeamC'])},
                        {'key': 'Project', 'value': f'Project-{random.randint(1, 20)}'},
                        {'key': 'CostCenter', 'value': f'CC-{random.randint(1000, 9999)}'},
                        {'key': 'Application', 'value': random.choice(['WebApp', 'Database', 'Cache', 'LoadBalancer'])},
                        {'key': 'Backup', 'value': instance_data['backup_tag']},
                        {'key': 'Monitoring', 'value': random.choice(['enabled', 'disabled'])},
                    ]

                    # Add some random additional tags
                    if random.choice([True, False]):
                        sample_tags.append({'key': 'Department', 'value': random.choice(['IT', 'Finance', 'HR', 'Operations'])})
                    if random.choice([True, False]):
                        sample_tags.append({'key': 'Compliance', 'value': random.choice(['SOX', 'HIPAA', 'PCI', 'None'])})

                    # Create and associate tags
                    for tag_data in sample_tags:
                        tag, _ = InstanceTag.objects.get_or_create(
                            key=tag_data['key'],
                            value=tag_data['value']
                        )
                        instance.tags.add(tag)

                    # Create SSM status for the instance
                    ssm_data = {
                        'instance': instance,
                        'ping_status': random.choice(['Online', 'Connection Lost', 'Inactive']),
                        'agent_version': f'3.1.{random.randint(1000, 1999)}.0',
                        'is_latest_version': random.choice([True, False]),
                        'last_ping_datetime': timezone.now() - timedelta(minutes=random.randint(1, 1440)),
                        'platform_name': random.choice(['Amazon Linux', 'Ubuntu', 'Windows Server', 'CentOS']),
                        'platform_type': random.choice(['Linux', 'Windows']),
                        'ssm_configured': random.choice([True, False])
                    }

                    SSMStatus.objects.create(**ssm_data)

                    self.stdout.write(f'Created instance: {instance.instance_id} in {account.account_name}')

                instance_counter += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully loaded sample data:\n'
                f'- {AWSAccount.objects.count()} AWS accounts\n'
                f'- {EC2Instance.objects.count()} EC2 instances\n'
                f'- {SSMStatus.objects.count()} SSM status records\n'
                f'- {InstanceTag.objects.count()} unique instance tags'
            )
        )
